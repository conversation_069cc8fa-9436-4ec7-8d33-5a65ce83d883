"""
自定义异常类
"""
from typing import Optional, Dict, Any


class WebAssistantException(Exception):
    """Web助手基础异常类"""
    
    def __init__(
        self,
        message: str,
        error_code: str = "UNKNOWN_ERROR",
        status_code: int = 500,
        details: Optional[Dict[str, Any]] = None
    ):
        self.message = message
        self.error_code = error_code
        self.status_code = status_code
        self.details = details or {}
        super().__init__(self.message)


class ValidationException(WebAssistantException):
    """验证异常"""
    
    def __init__(self, message: str, details: Optional[Dict[str, Any]] = None):
        super().__init__(
            message=message,
            error_code="VALIDATION_ERROR",
            status_code=400,
            details=details
        )


class AuthenticationException(WebAssistantException):
    """认证异常"""
    
    def __init__(self, message: str = "认证失败"):
        super().__init__(
            message=message,
            error_code="AUTHENTICATION_ERROR",
            status_code=401
        )


class AuthorizationException(WebAssistantException):
    """授权异常"""
    
    def __init__(self, message: str = "权限不足"):
        super().__init__(
            message=message,
            error_code="AUTHORIZATION_ERROR",
            status_code=403
        )


class ResourceNotFoundException(WebAssistantException):
    """资源未找到异常"""
    
    def __init__(self, message: str = "资源未找到"):
        super().__init__(
            message=message,
            error_code="RESOURCE_NOT_FOUND",
            status_code=404
        )


class RateLimitException(WebAssistantException):
    """限流异常"""
    
    def __init__(self, message: str = "请求过于频繁"):
        super().__init__(
            message=message,
            error_code="RATE_LIMIT_EXCEEDED",
            status_code=429
        )


class AgentException(WebAssistantException):
    """智能体异常"""
    
    def __init__(self, message: str, agent_type: str = "unknown"):
        super().__init__(
            message=message,
            error_code="AGENT_ERROR",
            status_code=500,
            details={"agent_type": agent_type}
        )


class ModelException(WebAssistantException):
    """模型异常"""
    
    def __init__(self, message: str, model_name: str = "unknown"):
        super().__init__(
            message=message,
            error_code="MODEL_ERROR",
            status_code=500,
            details={"model_name": model_name}
        )


class ExternalServiceException(WebAssistantException):
    """外部服务异常"""
    
    def __init__(self, message: str, service_name: str = "unknown"):
        super().__init__(
            message=message,
            error_code="EXTERNAL_SERVICE_ERROR",
            status_code=502,
            details={"service_name": service_name}
        )
