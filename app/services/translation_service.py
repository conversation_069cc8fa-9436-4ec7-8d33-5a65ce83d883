"""
翻译服务 - 支持多种翻译API提供商
"""
import aiohttp
import asyncio
import json
from typing import Dict, Any, Optional, List, AsyncGenerator
from dataclasses import dataclass
from enum import Enum

from app.config import settings
from app.services.llm_service import llm_service
from app.agents.translation.prompts import build_translation_messages
from app.utils.logger import get_logger

logger = get_logger(__name__)


class TranslationProvider(str, Enum):
    """翻译服务提供商"""
    DOUBAO = "doubao"
    LLM_TRANSLATE = "llm_translate"


@dataclass
class TranslationRequest:
    """翻译请求数据结构"""
    question: List[Dict[str, str]]  # [{"text": "hello"}, {"text": "world"}]
    stream: bool = False
    translate_options: Dict[str, str] = None  # {"src_lang": "en", "tgt_lang": "zh"}
    provider: str = "doubao"


@dataclass
class TranslationResult:
    """翻译结果数据结构"""
    code: str
    message: str
    data: List[Dict[str, str]]  # [{"text": "hello", "translateText": "你好"}]


class DoubaoTranslationClient:
    """Doubao翻译API客户端"""

    def __init__(self):
        self.api_url = settings.doubao_translation_api_url
        self.headers = {
            "appSysId": settings.doubao_app_sys_id,
            "token": settings.doubao_token,
            "Content-Type": "application/json"
        }
        self.session = None
        self._timeout = aiohttp.ClientTimeout(total=180)  # 3分钟超时

    async def _get_session(self) -> aiohttp.ClientSession:
        """获取HTTP会话"""
        if self.session is None or self.session.closed:
            self.session = aiohttp.ClientSession(timeout=self._timeout)
        return self.session

    def _build_payload(self, request: TranslationRequest) -> Dict[str, Any]:
        """构建Doubao API请求载荷"""
        # 从question数组中提取所有text值
        text_list = [item["text"] for item in request.question]
        
        translate_options = request.translate_options or {}
        
        return {
            "data": {
                "text": text_list,
                "src_lang": translate_options.get("src_lang", "en"),
                "tgt_lang": translate_options.get("tgt_lang", "zh"),
                "model_name": "doubao"
            }
        }

    async def translate(self, request: TranslationRequest) -> TranslationResult:
        """执行Doubao翻译"""
        session = await self._get_session()
        payload = self._build_payload(request)
        
        logger.info(f"=== Doubao翻译请求调试信息 ===")
        logger.info(f"URL: {self.api_url}")
        logger.info(f"Headers: {self.headers}")
        logger.info(f"Payload: {json.dumps(payload, ensure_ascii=False, indent=2)}")
        
        try:
            logger.info(f"开始执行Doubao翻译，文本数量: {len(request.question)}")
            
            async with session.post(
                self.api_url,
                headers=self.headers,
                json=payload
            ) as response:
                logger.info(f"=== Doubao翻译响应调试信息 ===")
                logger.info(f"状态码: {response.status}")
                logger.info(f"响应头: {dict(response.headers)}")
                
                response_text = await response.text()
                logger.info(f"响应内容: {response_text}")
                
                if response.status == 200:
                    try:
                        result = json.loads(response_text)
                        logger.info(f"JSON解析成功，开始解析翻译结果...")
                        parsed_result = self._parse_doubao_result(result, request.question)
                        logger.info(f"Doubao翻译完成: code={parsed_result.code}")
                        return parsed_result
                    except json.JSONDecodeError as e:
                        logger.error(f"JSON解析失败: {str(e)}")
                        logger.error(f"原始响应: {response_text}")
                        return TranslationResult(
                            code="error",
                            message=f"响应解析失败: {str(e)}",
                            data=[]
                        )
                else:
                    logger.error(f"Doubao翻译请求失败: {response.status}")
                    logger.error(f"错误内容: {response_text}")
                    return TranslationResult(
                        code="error",
                        message=f"请求失败: HTTP {response.status}",
                        data=[]
                    )
                    
        except Exception as e:
            logger.error(f"Doubao翻译执行异常: {str(e)}", exc_info=True)
            return TranslationResult(
                code="error",
                message=f"翻译异常: {str(e)}",
                data=[]
            )

    def _parse_doubao_result(self, response: Dict[str, Any], original_questions: List[Dict[str, str]]) -> TranslationResult:
        """解析Doubao API响应为标准格式"""
        logger.info(f"=== 开始解析Doubao翻译结果 ===")
        logger.info(f"响应类型: {type(response)}")
        logger.info(f"响应键: {list(response.keys()) if isinstance(response, dict) else 'Not a dict'}")
        
        try:
            # 根据实际API响应格式调整解析逻辑
            if "code" in response and response["code"] == 0:
                # 假设成功响应格式
                translated_texts = response.get("data", {}).get("translated_text", [])
                
                # 构建结果数据
                result_data = []
                for i, question in enumerate(original_questions):
                    translated_text = translated_texts[i] if i < len(translated_texts) else question["text"]
                    result_data.append({
                        "text": question["text"],
                        "translateText": translated_text
                    })
                
                return TranslationResult(
                    code="success",
                    message="翻译成功",
                    data=result_data
                )
            else:
                logger.warning(f"Doubao API返回错误: {response}")
                return TranslationResult(
                    code="error",
                    message=response.get("message", "翻译失败"),
                    data=[]
                )
                        
        except Exception as e:
            logger.error(f"解析Doubao翻译结果失败: {str(e)}", exc_info=True)
            logger.error(f"响应内容: {json.dumps(response, ensure_ascii=False, indent=2)}")
            return TranslationResult(
                code="error",
                message=f"结果解析异常: {str(e)}",
                data=[]
            )

    async def close(self):
        """关闭会话"""
        if self.session and not self.session.closed:
            await self.session.close()
            self.session = None


class LLMTranslationClient:
    """基于LLM的翻译客户端"""

    async def translate(self, request: TranslationRequest) -> TranslationResult:
        """执行LLM翻译"""
        try:
            translate_options = request.translate_options or {}
            src_lang = translate_options.get("src_lang", "en")
            tgt_lang = translate_options.get("tgt_lang", "zh")

            result_data = []

            # 逐个翻译每个文本
            for question_item in request.question:
                text = question_item["text"]

                # 构建翻译消息
                messages = build_translation_messages(text, src_lang, tgt_lang)

                # 调用LLM服务
                response = await llm_service.chat_completion(
                    messages=messages,
                    model=settings.default_llm_model,
                    max_tokens=len(text) * 3,  # 动态调整token数量
                    temperature=0.3
                )

                # 提取翻译结果
                translated_text = self._extract_translation(response)

                result_data.append({
                    "text": text,
                    "translateText": translated_text
                })

            return TranslationResult(
                code="success",
                message="翻译成功",
                data=result_data
            )

        except Exception as e:
            logger.error(f"LLM翻译失败: {str(e)}", exc_info=True)
            return TranslationResult(
                code="error",
                message=f"LLM翻译异常: {str(e)}",
                data=[]
            )

    async def stream_translate(self, request: TranslationRequest) -> AsyncGenerator[str, None]:
        """执行LLM流式翻译 - 支持并发处理和标准化输出格式"""
        try:
            translate_options = request.translate_options or {}
            src_lang = translate_options.get("src_lang", "en")
            tgt_lang = translate_options.get("tgt_lang", "zh")

            logger.info(f"开始并发流式翻译 {len(request.question)} 个文本")

            # 创建并发翻译任务
            tasks = []
            for idx, question_item in enumerate(request.question):
                task = self._stream_translate_single_text(
                    text=question_item["text"],
                    index=idx,
                    src_lang=src_lang,
                    tgt_lang=tgt_lang
                )
                tasks.append(task)

            # 使用异步生成器合并多个流式输出
            async def merge_streams():
                """合并多个流式输出"""
                # 创建队列收集所有流式数据
                output_queue = asyncio.Queue()
                active_tasks = set()

                # 为每个任务创建收集器
                async def collect_from_stream(stream_gen, task_id):
                    try:
                        async for chunk in stream_gen:
                            await output_queue.put((task_id, chunk))
                    except Exception as e:
                        logger.error(f"收集流式数据失败 (任务 {task_id}): {str(e)}")
                    finally:
                        active_tasks.discard(task_id)
                        if not active_tasks:
                            await output_queue.put((None, None))  # 完成信号

                # 启动所有收集器
                for i, task in enumerate(tasks):
                    active_tasks.add(i)
                    asyncio.create_task(collect_from_stream(task, i))

                # 输出合并的流式数据
                while True:
                    try:
                        task_id, chunk = await asyncio.wait_for(output_queue.get(), timeout=60.0)
                        if task_id is None:  # 完成信号
                            break
                        yield chunk
                    except asyncio.TimeoutError:
                        logger.warning("流式翻译合并超时")
                        break
                    except Exception as e:
                        logger.error(f"流式数据合并异常: {str(e)}")
                        break

            # 输出合并后的流式数据
            async for chunk in merge_streams():
                yield chunk

            # 发送最终完成响应
            yield "data: [DONE]\n\n"
            logger.info("并发流式翻译完成")

        except Exception as e:
            logger.error(f"LLM流式翻译失败: {str(e)}", exc_info=True)
            error_response = {
                "delta": {
                    "error": True,
                    "message": f"LLM流式翻译异常: {str(e)}"
                }
            }
            yield f"data: {json.dumps(error_response, ensure_ascii=False)}\n\n"
            yield "data: [DONE]\n\n"

    async def _stream_translate_single_text(self, text: str, index: int, src_lang: str, tgt_lang: str) -> AsyncGenerator[str, None]:
        """流式翻译单个文本 - 使用标准化增量格式"""
        try:
            logger.info(f"开始流式翻译文本 {index}: {text[:50]}...")

            # 构建翻译消息
            messages = build_translation_messages(text, src_lang, tgt_lang)

            # 调用LLM流式服务
            stream_generator = llm_service.stream_chat_completion(
                messages=messages,
                model=settings.default_llm_model,
                max_tokens=len(text) * 3,
                temperature=0.3
            )

            # 处理流式响应
            accumulated_content = ""
            async for chunk in stream_generator:
                try:
                    # 处理不同格式的SSE数据
                    chunk_data = chunk.strip()

                    # 如果包含"data: "前缀，移除它
                    if chunk_data.startswith("data: "):
                        chunk_data = chunk_data[6:]

                    # 跳过空行和结束标记
                    if not chunk_data or chunk_data == "[DONE]":
                        if chunk_data == "[DONE]":
                            break
                        continue

                    # 解析JSON数据
                    chunk_json = json.loads(chunk_data)

                    # 提取增量内容
                    if "choices" in chunk_json and chunk_json["choices"]:
                        choice = chunk_json["choices"][0]
                        if "delta" in choice and "content" in choice["delta"]:
                            delta_content = choice["delta"]["content"]
                            if delta_content:
                                accumulated_content = delta_content

                                # 构建标准化增量响应格式
                                delta_response = {
                                    "delta": {
                                        "index": index,
                                        "text": text,
                                        "translateText": accumulated_content
                                    }
                                }

                                yield f"data: {json.dumps(delta_response, ensure_ascii=False)}\n\n"

                except (json.JSONDecodeError, KeyError) as e:
                    logger.warning(f"解析流式数据失败 (文本 {index}): {str(e)}, chunk: {chunk}")
                    continue

            # 发送该文本的完成标记
            completion_response = {
                "delta": {
                    "index": index,
                    "text": text,
                    "translateText": accumulated_content,
                    "finished": True
                }
            }
            yield f"data: {json.dumps(completion_response, ensure_ascii=False)}\n\n"

            logger.info(f"文本 {index} 翻译完成，最终长度: {len(accumulated_content)}")

        except Exception as e:
            logger.error(f"单个文本流式翻译失败 (文本 {index}): {str(e)}", exc_info=True)
            error_response = {
                "delta": {
                    "index": index,
                    "text": text,
                    "error": True,
                    "message": f"翻译异常: {str(e)}"
                }
            }
            yield f"data: {json.dumps(error_response, ensure_ascii=False)}\n\n"

    def _extract_translation(self, response: Any) -> str:
        """从LLM响应中提取翻译文本"""
        try:
            if isinstance(response, dict):
                choices = response.get("choices", [])
                if choices:
                    message = choices[0].get("message", {})
                    content = message.get("content", "")
                    if isinstance(content, str):
                        return content.strip()
            elif isinstance(response, str):
                return response.strip()
        except Exception as e:
            logger.error(f"提取翻译结果失败: {str(e)}")
        
        return ""


class TranslationService:
    """翻译服务管理器"""

    def __init__(self):
        self.doubao_client = DoubaoTranslationClient()
        self.llm_client = LLMTranslationClient()
        logger.info("初始化翻译服务")

    async def translate(self, request: TranslationRequest) -> TranslationResult:
        """执行翻译"""
        logger.info(f"=== TranslationService.translate 调用 ===")
        logger.info(f"Provider: {request.provider}")
        logger.info(f"Stream: {request.stream}")

        try:
            # 判断是否为流式请求
            if request.stream and request.provider == TranslationProvider.LLM_TRANSLATE:
                # LLM提供商 + 流式模式：不应该在这里调用，应该使用stream_translate
                logger.warning("流式翻译请求应该使用stream_translate方法")
                # 为了兼容性，仍然执行非流式翻译
                result = await self.llm_client.translate(request)
            elif request.provider == TranslationProvider.DOUBAO:
                # Doubao提供商：始终使用非流式翻译
                result = await self.doubao_client.translate(request)
            elif request.provider == TranslationProvider.LLM_TRANSLATE:
                # LLM提供商 + 非流式模式
                result = await self.llm_client.translate(request)
            else:
                # 默认使用Doubao
                logger.warning(f"未知的翻译提供商: {request.provider}，使用默认的Doubao")
                result = await self.doubao_client.translate(request)

            logger.info(f"TranslationService.translate 返回结果: code={result.code}")
            return result

        except Exception as e:
            logger.error(f"翻译服务执行异常: {str(e)}", exc_info=True)
            return TranslationResult(
                code="error",
                message=f"翻译服务异常: {str(e)}",
                data=[]
            )

    async def stream_translate(self, request: TranslationRequest) -> AsyncGenerator[str, None]:
        """流式翻译"""
        logger.info(f"=== TranslationService.stream_translate 调用 ===")
        logger.info(f"Provider: {request.provider}")

        try:
            # 判断提供商类型和流式支持
            if request.provider == TranslationProvider.DOUBAO:
                # Doubao提供商不支持流式，返回非流式结果的流式包装
                logger.info("Doubao提供商不支持流式，使用非流式结果包装")
                result = await self.doubao_client.translate(request)

                response_data = {
                    "code": result.code,
                    "message": result.message,
                    "data": result.data
                }

                yield f"data: {json.dumps(response_data, ensure_ascii=False)}\n\n"
                yield "data: [DONE]\n\n"

            elif request.provider == TranslationProvider.LLM_TRANSLATE:
                # LLM提供商支持真正的流式翻译
                logger.info("LLM提供商支持流式翻译，开始流式处理")
                async for chunk in self.llm_client.stream_translate(request):
                    yield chunk

            else:
                # 未知提供商，默认使用Doubao的非流式包装
                logger.warning(f"未知的翻译提供商: {request.provider}，使用默认的Doubao非流式包装")
                result = await self.doubao_client.translate(request)

                response_data = {
                    "code": result.code,
                    "message": result.message,
                    "data": result.data
                }

                yield f"data: {json.dumps(response_data, ensure_ascii=False)}\n\n"
                yield "data: [DONE]\n\n"

        except Exception as e:
            logger.error(f"流式翻译服务执行异常: {str(e)}", exc_info=True)
            error_response = {
                "code": "error",
                "message": f"流式翻译异常: {str(e)}",
                "data": []
            }
            yield f"data: {json.dumps(error_response, ensure_ascii=False)}\n\n"
            yield "data: [DONE]\n\n"

    async def close(self):
        """关闭服务"""
        await self.doubao_client.close()


# 全局翻译服务实例
translation_service = TranslationService()
