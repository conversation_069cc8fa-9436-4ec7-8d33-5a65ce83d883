"""
联网搜索服务
"""
import asyncio
import aiohttp
import json
from typing import Dict, Any, Optional, List, Union
from dataclasses import dataclass

from app.config import settings
from app.utils.logger import get_logger

logger = get_logger(__name__)


@dataclass
class SearchRequest:
    """搜索请求数据结构"""
    query: str
    provider: Union[str, List[str]] = "hiagent"
    snippet: bool = False
    authInfos: Optional[Dict[str, Dict[str, Any]]] = None


@dataclass 
class SearchResult:
    """搜索结果数据结构"""
    title: str
    content: str
    url: str
    source: str


class WebSearchClient:
    """联网搜索客户端"""

    def __init__(self, api_url: str = "http://*************/fst/api/v1/websearch"):
        self.api_url = api_url
        self.session = None
        self._timeout = aiohttp.ClientTimeout(total=180)  # 3分钟超时

    async def _get_session(self) -> aiohttp.ClientSession:
        """获取HTTP会话"""
        if self.session is None or self.session.closed:
            self.session = aiohttp.ClientSession(timeout=self._timeout)
        return self.session

    def _build_payload(self, request: SearchRequest) -> Dict[str, Any]:
        """构建搜索请求载荷"""
        payload = {
            "query": request.query,
            "provider": request.provider,
            "snippet": request.snippet
        }
        
        if request.authInfos:
            payload["authInfos"] = request.authInfos
            
        return payload

    async def search(self, request: SearchRequest) -> List[SearchResult]:
        """执行搜索并返回结构化结果"""
        session = await self._get_session()
        payload = self._build_payload(request)
        
        # 详细调试日志
        headers = {
            "Content-Type": "application/json",
            "User-Agent": "Apipost client Runtime/+https://www.apipost.cn/"
        }
        
        logger.info(f"=== 搜索请求调试信息 ===")
        logger.info(f"URL: {self.api_url}")
        logger.info(f"Headers: {headers}")
        logger.info(f"Payload: {json.dumps(payload, ensure_ascii=False, indent=2)}")
        
        try:
            logger.info(f"开始执行联网搜索: {request.query}")
            
            async with session.post(
                self.api_url,
                headers=headers,
                json=payload
            ) as response:
                logger.info(f"=== 搜索响应调试信息 ===")
                logger.info(f"状态码: {response.status}")
                logger.info(f"响应头: {dict(response.headers)}")
                
                response_text = await response.text()
                logger.info(f"响应内容: {response_text}")
                
                if response.status == 200:
                    try:
                        result = json.loads(response_text)
                        logger.info(f"JSON解析成功，开始解析搜索结果...")
                        parsed_results = self._parse_search_results(result, request.provider)
                        logger.info(f"最终解析出 {len(parsed_results)} 条结果")
                        return parsed_results
                    except json.JSONDecodeError as e:
                        logger.error(f"JSON解析失败: {str(e)}")
                        logger.error(f"原始响应: {response_text}")
                        return []
                else:
                    logger.error(f"搜索请求失败: {response.status}")
                    logger.error(f"错误内容: {response_text}")
                    return []
                    
        except Exception as e:
            logger.error(f"搜索执行异常: {str(e)}", exc_info=True)
            return []

    def _parse_search_results(self, response: Dict[str, Any], provider: Union[str, List[str]]) -> List[SearchResult]:
        """解析搜索API响应为标准格式"""
        results = []
        
        logger.info(f"=== 开始解析搜索结果 ===")
        logger.info(f"响应类型: {type(response)}")
        logger.info(f"响应键: {list(response.keys()) if isinstance(response, dict) else 'Not a dict'}")
        
        try:
            # 根据实际API响应格式解析
            if "code" in response:
                logger.info(f"响应code: {response['code']}")
                if response["code"] == "0":
                    # 检查data字段
                    data = response.get("data", {})
                    logger.info(f"data类型: {type(data)}")
                    logger.info(f"data键: {list(data.keys()) if isinstance(data, dict) else 'Not a dict'}")
                    
                    if isinstance(data, dict) and "results" in data:
                        # data.results是结果列表
                        search_results = data["results"]
                        logger.info(f"search_results类型: {type(search_results)}")
                        logger.info(f"search_results长度: {len(search_results) if isinstance(search_results, list) else 'Not a list'}")
                        
                        if isinstance(search_results, list):
                            for i, item in enumerate(search_results):
                                logger.info(f"处理第{i+1}个结果: {list(item.keys()) if isinstance(item, dict) else 'Not a dict'}")
                                result = SearchResult(
                                    title=item.get("title", ""),
                                    content=item.get("content", ""),
                                    url=item.get("url", ""),
                                    source=item.get("providerName", item.get("provider", str(provider)))
                                )
                                results.append(result)
                                logger.info(f"结果{i+1}: title='{result.title}', source='{result.source}', url='{result.url}'")
                    elif isinstance(data, list):
                        # data直接是结果列表（向后兼容）
                        logger.info("data是直接的结果列表")
                        for i, item in enumerate(data):
                            logger.info(f"处理第{i+1}个结果: {list(item.keys()) if isinstance(item, dict) else 'Not a dict'}")
                            result = SearchResult(
                                title=item.get("title", ""),
                                content=item.get("content", ""),
                                url=item.get("url", ""),
                                source=item.get("providerName", item.get("provider", str(provider)))
                            )
                            results.append(result)
                            logger.info(f"结果{i+1}: title='{result.title}', source='{result.source}', url='{result.url}'")
                    else:
                        logger.warning(f"未识别的data格式: {type(data)}, 内容: {data}")
                else:
                    logger.warning(f"响应code不是'0': {response['code']}")
                    logger.warning(f"完整错误响应: {json.dumps(response, ensure_ascii=False, indent=2)}")
            else:
                logger.warning("响应中没有'code'字段")
                logger.warning(f"完整响应: {json.dumps(response, ensure_ascii=False, indent=2)}")
                        
            logger.info(f"=== 解析完成，共 {len(results)} 条结果 ===")
            
        except Exception as e:
            logger.error(f"解析搜索结果失败: {str(e)}", exc_info=True)
            logger.error(f"响应内容: {json.dumps(response, ensure_ascii=False, indent=2)}")
            
        return results

    async def close(self):
        """关闭会话"""
        if self.session and not self.session.closed:
            await self.session.close()
            self.session = None


class WebSearchService:
    """联网搜索服务管理器"""

    def __init__(self):
        # 使用配置中的搜索API URL，如果没有设置则使用默认值
        search_api_url = getattr(settings, 'web_search_api_url', 'http://*************/fst/api/v1/websearch')
        self.client = WebSearchClient(api_url=search_api_url)
        logger.info("初始化联网搜索服务")

    async def search(self, 
                    query: str,
                    provider: Union[str, List[str]] = "hiagent",
                    snippet: bool = False,
                    authInfos: Optional[Dict[str, Dict[str, Any]]] = None) -> List[SearchResult]:
        """执行联网搜索"""
        logger.info(f"=== WebSearchService.search 调用 ===")
        logger.info(f"query: {query}")
        logger.info(f"provider: {provider}")
        logger.info(f"snippet: {snippet}")
        logger.info(f"authInfos: {authInfos is not None}")
        
        request = SearchRequest(
            query=query,
            provider=provider,
            snippet=snippet,
            authInfos=authInfos
        )
        
        results = await self.client.search(request)
        logger.info(f"WebSearchService.search 返回 {len(results)} 条结果")
        return results

    def format_search_results(self, results: List[SearchResult], max_results: int = 5) -> str:
        """格式化搜索结果为文本，按provider分类"""
        if not results:
            return "未找到相关搜索结果。"
        
        # 按provider分类
        wiki_results = []
        hiagent_results = []
        
        for result in results[:max_results]:
            if "wiki" in result.source.lower():
                wiki_results.append(result)
            else:
                hiagent_results.append(result)
        
        formatted_sections = []
        
        # 联网搜索结果
        if hiagent_results:
            formatted_sections.append("=== 🌐 联网搜索结果 ===")
            for i, result in enumerate(hiagent_results, 1):
                formatted_result = f"""
【联网搜索 {i}】
标题: {result.title}
来源: {result.source}
链接: {result.url}
内容: {result.content[:500]}{"..." if len(result.content) > 500 else ""}
"""
                formatted_sections.append(formatted_result.strip())
        
        # Wiki搜索结果
        if wiki_results:
            formatted_sections.append("\n=== 📚 Wiki搜索结果 ===")
            for i, result in enumerate(wiki_results, 1):
                formatted_result = f"""
【Wiki搜索 {i}】
标题: {result.title}
来源: {result.source}
链接: {result.url}
内容: {result.content[:500]}{"..." if len(result.content) > 500 else ""}
"""
                formatted_sections.append(formatted_result.strip())
        
        return "\n\n".join(formatted_sections)

    def format_reference_links(self, results: List[SearchResult], max_results: int = 5) -> str:
        """格式化参考链接，按provider分类"""
        if not results:
            return ""
        
        # 按provider分类
        wiki_results = []
        hiagent_results = []
        
        for result in results[:max_results]:
            if "wiki" in result.source.lower():
                wiki_results.append(result)
            else:
                hiagent_results.append(result)
        
        links_sections = []
        
        # 联网搜索链接
        if hiagent_results:
            links_sections.append("### 🌐 联网搜索")
            for i, result in enumerate(hiagent_results, 1):
                links_sections.append(f"{i}. [{result.title}]({result.url})")
        
        # Wiki搜索链接
        if wiki_results:
            links_sections.append("\n### 📚 Wiki搜索")
            for i, result in enumerate(wiki_results, 1):
                links_sections.append(f"{i}. [{result.title}]({result.url})")
        
        if links_sections:
            return "\n\n## 📚 参考链接\n\n" + "\n".join(links_sections)
        return ""

    async def close(self):
        """关闭服务"""
        await self.client.close()


# 全局搜索服务实例
web_search_service = WebSearchService()
