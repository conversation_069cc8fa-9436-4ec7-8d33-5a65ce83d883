"""
图片解析服务
"""
import aiohttp
import json
from typing import Dict, Any, Optional
from dataclasses import dataclass

from app.config import settings
from app.utils.logger import get_logger

logger = get_logger(__name__)


@dataclass
class ImageParseRequest:
    """图片解析请求数据结构"""
    url: str


@dataclass
class ImageParseResult:
    """图片解析结果数据结构"""
    success: bool
    output: str
    message: str


class ImageParseClient:
    """图片解析客户端"""

    def __init__(self, api_url: str = "http://10.102.75.112/fst/api/v1/image-parse"):
        self.api_url = api_url
        self.session = None
        self._timeout = aiohttp.ClientTimeout(total=180)  # 3分钟超时

    async def _get_session(self) -> aiohttp.ClientSession:
        """获取HTTP会话"""
        if self.session is None or self.session.closed:
            self.session = aiohttp.ClientSession(timeout=self._timeout)
        return self.session

    def _build_payload(self, request: ImageParseRequest) -> Dict[str, Any]:
        """构建图片解析请求载荷"""
        return {"url": request.url}

    async def parse_image(self, request: ImageParseRequest) -> ImageParseResult:
        """执行图片解析并返回结构化结果"""
        session = await self._get_session()
        payload = self._build_payload(request)
        
        headers = {
            "Content-Type": "application/json",
            "User-Agent": "Apipost client Runtime/+https://www.apipost.cn/"
        }
        
        logger.info(f"=== 图片解析请求调试信息 ===")
        logger.info(f"URL: {self.api_url}")
        logger.info(f"Headers: {headers}")
        logger.info(f"Payload: {json.dumps(payload, ensure_ascii=False, indent=2)}")
        
        try:
            logger.info(f"开始执行图片解析: {request.url}")
            
            async with session.post(
                self.api_url,
                headers=headers,
                json=payload
            ) as response:
                logger.info(f"=== 图片解析响应调试信息 ===")
                logger.info(f"状态码: {response.status}")
                logger.info(f"响应头: {dict(response.headers)}")
                
                response_text = await response.text()
                logger.info(f"响应内容: {response_text}")
                
                if response.status == 200:
                    try:
                        result = json.loads(response_text)
                        logger.info(f"JSON解析成功，开始解析图片解析结果...")
                        parsed_result = self._parse_image_result(result)
                        logger.info(f"图片解析完成: success={parsed_result.success}")
                        return parsed_result
                    except json.JSONDecodeError as e:
                        logger.error(f"JSON解析失败: {str(e)}")
                        logger.error(f"原始响应: {response_text}")
                        return ImageParseResult(
                            success=False,
                            output="",
                            message=f"响应解析失败: {str(e)}"
                        )
                else:
                    logger.error(f"图片解析请求失败: {response.status}")
                    logger.error(f"错误内容: {response_text}")
                    return ImageParseResult(
                        success=False,
                        output="",
                        message=f"请求失败: HTTP {response.status}"
                    )
                    
        except Exception as e:
            logger.error(f"图片解析执行异常: {str(e)}", exc_info=True)
            return ImageParseResult(
                success=False,
                output="",
                message=f"解析异常: {str(e)}"
            )

    def _parse_image_result(self, response: Dict[str, Any]) -> ImageParseResult:
        """解析图片解析API响应为标准格式"""
        logger.info(f"=== 开始解析图片解析结果 ===")
        logger.info(f"响应类型: {type(response)}")
        logger.info(f"响应键: {list(response.keys()) if isinstance(response, dict) else 'Not a dict'}")
        
        try:
            if "code" in response:
                logger.info(f"响应code: {response['code']}")
                if response["code"] == "0":
                    data = response.get("data", {})
                    logger.info(f"data类型: {type(data)}")
                    logger.info(f"data键: {list(data.keys()) if isinstance(data, dict) else 'Not a dict'}")
                    
                    if isinstance(data, dict):
                        success = data.get("success", False)
                        result = data.get("result", {})
                        message = data.get("message", "")
                        
                        if isinstance(result, dict):
                            output = result.get("output", "")
                        else:
                            output = str(result) if result else ""
                        
                        logger.info(f"解析结果: success={success}, output长度={len(output)}, message={message}")
                        
                        return ImageParseResult(
                            success=success,
                            output=output,
                            message=message
                        )
                    else:
                        logger.warning(f"未识别的data格式: {type(data)}, 内容: {data}")
                        return ImageParseResult(
                            success=False,
                            output="",
                            message="数据格式异常"
                        )
                else:
                    logger.warning(f"响应code不是'0': {response['code']}")
                    return ImageParseResult(
                        success=False,
                        output="",
                        message=response.get("msg", "解析失败")
                    )
            else:
                logger.warning("响应中没有'code'字段")
                return ImageParseResult(
                    success=False,
                    output="",
                    message="响应格式异常"
                )
                        
        except Exception as e:
            logger.error(f"解析图片解析结果失败: {str(e)}", exc_info=True)
            logger.error(f"响应内容: {json.dumps(response, ensure_ascii=False, indent=2)}")
            return ImageParseResult(
                success=False,
                output="",
                message=f"结果解析异常: {str(e)}"
            )

    async def close(self):
        """关闭会话"""
        if self.session and not self.session.closed:
            await self.session.close()
            self.session = None


class ImageParseService:
    """图片解析服务管理器"""

    def __init__(self):
        # 使用配置中的图片解析API URL，如果没有设置则使用默认值
        image_parse_api_url = getattr(settings, 'image_parse_api_url', 'http://10.102.75.112/fst/api/v1/image-parse')
        self.client = ImageParseClient(api_url=image_parse_api_url)
        logger.info("初始化图片解析服务")

    async def parse_image(self, url: str) -> ImageParseResult:
        """执行图片解析"""
        logger.info(f"=== ImageParseService.parse_image 调用 ===")
        logger.info(f"图片URL: {url}")
        
        request = ImageParseRequest(url=url)
        
        result = await self.client.parse_image(request)
        logger.info(f"ImageParseService.parse_image 返回结果: success={result.success}")
        return result

    async def close(self):
        """关闭服务"""
        await self.client.close()


# 全局图片解析服务实例
image_parse_service = ImageParseService()
