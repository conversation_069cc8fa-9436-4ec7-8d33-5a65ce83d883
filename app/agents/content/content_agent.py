"""
Content summarization agent with LangGraph workflow - direct LLM passthrough
"""
from typing import Op<PERSON>, AsyncGenerator, Union, Dict, Any, List
import json

from langgraph.graph import StateGraph, END

from app.agents.base import BaseAgent, AgentWorkflowState
from app.core.models import AgentType, TaskStatus
from app.services.llm_service import llm_service
from app.utils.logger import get_logger
from app.config import settings
from .context_utils import (
    estimate_messages_tokens,
    fit_messages_into_budget,
    truncate_messages_into_budget,
    prepare_long_content_for_final,
)
from .prompts import SUMMARY_SYSTEM_PROMPT, build_content_summary_messages

logger = get_logger(__name__)


class ContentUnderstandingAgent(BaseAgent):
    """Content summarization agent with prompt processing and LLM passthrough"""

    def __init__(self, agent_type: AgentType):
        super().__init__(
            agent_type=agent_type,
            name="Content Summarization Agent",
            description="Processes content with prompt engineering and returns raw LLM responses"
        )

    def _build_graph(self):
        """Build LangGraph workflow for content processing"""
        workflow = StateGraph(AgentWorkflowState)

        # Add processing nodes
        workflow.add_node("validate_input", self._validate_input_node)
        workflow.add_node("build_from_content", self._build_from_content_node)
        workflow.add_node("compact_history", self._compact_history_node)
        workflow.add_node("truncate_history", self._truncate_history_node)
        workflow.add_node("call_llm", self._call_llm_node)
        workflow.add_node("finalize", self._finalize_node)

        # Define workflow edges
        workflow.set_entry_point("validate_input")
        # route based on whether content is provided
        workflow.add_conditional_edges(
            "validate_input",
            self._route_after_validation,
            {
                "build_from_content": "build_from_content",
                "compact_history": "compact_history",
                "truncate_history": "truncate_history",
            },
        )
        workflow.add_edge("build_from_content", "call_llm")
        workflow.add_edge("compact_history", "call_llm")
        workflow.add_edge("truncate_history", "call_llm")
        workflow.add_edge("call_llm", "finalize")
        workflow.add_edge("finalize", END)

        # Compile the graph
        self.graph = workflow.compile()
        logger.info("LangGraph workflow built for content agent")

    async def process_node(self, state: AgentWorkflowState) -> AgentWorkflowState:
        """Required by BaseAgent interface"""
        return state

    async def _validate_input_node(self, state: AgentWorkflowState) -> AgentWorkflowState:
        """Validate and prepare input data"""
        try:
            state["current_step"] = "validate_input"
            input_data = state["input_data"]

            content = input_data.get("content")
            messages = input_data.get("messages")
            model = input_data.get("model")
            is_stream = input_data.get("stream", False)
            max_tokens = input_data.get("max_tokens") or settings.default_max_tokens

            if (content is None or content == "") and (not messages or not isinstance(messages, list) or len(messages) == 0):
                raise ValueError("Either content must be provided or messages must be a non-empty list")

            # Store validated data
            state["step_results"]["content"] = content
            state["step_results"]["messages"] = messages or []
            state["step_results"]["model"] = model
            state["step_results"]["is_stream"] = is_stream
            state["step_results"]["max_tokens"] = max_tokens
            state["step_results"]["has_content"] = bool(content)
            state["step_results"]["history_strategy"] = input_data.get("history_strategy", "truncate")

            if content:
                logger.info("Input validated - using direct content summarization")
            else:
                logger.info(f"Input validated - {len(messages)} messages")

        except Exception as e:
            state["error"] = f"Input validation failed: {str(e)}"
            logger.error(f"Input validation failed: {e}")

        return state

    async def _build_from_content_node(self, state: AgentWorkflowState) -> AgentWorkflowState:
        """Build final messages when content is provided"""
        try:
            state["current_step"] = "build_from_content"
            step_results = state["step_results"]
            content = step_results.get("content")

            model = step_results.get("model")
            # 计算预算
            context_window = settings.get_context_window(model)
            safety_margin = 300
            max_tokens = step_results.get("max_tokens", settings.default_max_tokens)
            allowed_input_tokens = max(2000, context_window - max_tokens - safety_margin)

            if settings.token_count(content) > allowed_input_tokens:
                # 内容过长：
                # 方案：并发压缩前N-1块 + 合并，拼接最近片段全文，作为最终prompt输入
                # 优点：保持最终 call_llm 可流式/非流式受外部控制
                final_content = await prepare_long_content_for_final(
                    content, model, allowed_input_tokens
                )
                processed_messages = build_content_summary_messages(final_content)
                logger.info("Content too long; prepared pre-compressed summary + last chunk for final streaming call")
            else:
                processed_messages = build_content_summary_messages(content)

            token_count = estimate_messages_tokens(processed_messages)
            state["step_results"]["processed_messages"] = processed_messages
            state["step_results"]["prompt_tokens"] = token_count
            logger.info("Prompt processing completed - built messages from content")
        except Exception as e:
            state["error"] = f"Build from content failed: {str(e)}"
            logger.error(f"Build from content failed: {e}")
        return state


    async def _compact_history_node(self, state: AgentWorkflowState) -> AgentWorkflowState:
        """Compact history when only messages are provided.

        备注：
        - 基于模型上下文窗口（context_window）计算输入预算：allowed_input_tokens = context_window - max_tokens - safety_margin。
        - 使用 fit_messages_into_budget 在预算内压缩历史：
          1) 优先保留最新的 user 问题（不丢失最新意图）
          2) 对较早的历史对话进行摘要并插入为系统消息（必要时再截断摘要）
        - 最终生成的 processed_messages 会进入统一的 call_llm 节点，继续支持流式/非流式输出控制。
        """
        try:
            state["current_step"] = "compact_history"
            step_results = state["step_results"]
            model = step_results.get("model")
            max_tokens = step_results.get("max_tokens", settings.default_max_tokens)
            incoming_messages = step_results.get("messages", [])
            context_window = settings.get_context_window(model)

            safety_margin = 300
            allowed_input_tokens = max(1000, context_window - max_tokens - safety_margin)

            processed_messages, token_count = await fit_messages_into_budget(
                incoming_messages, allowed_input_tokens, model
            )

            state["step_results"]["processed_messages"] = processed_messages
            state["step_results"]["prompt_tokens"] = token_count

            logger.info(
                f"Prompt processing completed - history compacted to {len(processed_messages)} messages, tokens≈{token_count}"
            )
        except Exception as e:
            state["error"] = f"Compact history failed: {str(e)}"
            logger.error(f"Compact history failed: {e}")
        return state

    async def _truncate_history_node(self, state: AgentWorkflowState) -> AgentWorkflowState:
        """Truncate history by tokens without LLM summarization"""
        try:
            state["current_step"] = "truncate_history"
            step_results = state["step_results"]
            max_tokens = step_results.get("max_tokens", settings.default_max_tokens)
            model = step_results.get("model")
            incoming_messages = step_results.get("messages", [])

            context_window = settings.get_context_window(model)
            safety_margin = 300
            allowed_input_tokens = max(1000, context_window - max_tokens - safety_margin)

            processed_messages, token_count = truncate_messages_into_budget(
                incoming_messages, allowed_input_tokens
            )
            processed_messages.append({"role": "system", "content": SUMMARY_SYSTEM_PROMPT})
            state["step_results"]["processed_messages"] = processed_messages
            state["step_results"]["prompt_tokens"] = token_count
            logger.info(
                f"Prompt processing completed - history truncated to {len(processed_messages)} messages, tokens≈{token_count}"
            )
        except Exception as e:
            state["error"] = f"Truncate history failed: {str(e)}"
            logger.error(f"Truncate history failed: {e}")
        return state

    def _route_after_validation(self, state: AgentWorkflowState) -> str:
        """Route to content or history branch based on input and strategy"""
        sr = state.get("step_results", {})
        if sr.get("has_content", False):
            return "build_from_content"
        strategy = (sr.get("history_strategy") or "truncate").lower()
        if strategy == "summarize":
            return "compact_history"
        return "truncate_history"

    async def _call_llm_node(self, state: AgentWorkflowState) -> AgentWorkflowState:
        """Call LLM and store response (streaming or non-streaming)"""
        try:
            state["current_step"] = "call_llm"
            step_results = state["step_results"]

            messages = step_results.get("processed_messages", [])
            model = step_results.get("model")  # Get custom model
            is_stream = step_results.get("is_stream", False)
            max_tokens = step_results.get("max_tokens", settings.default_max_tokens)

            if is_stream:
                # For streaming: create and store the stream generator
                stream_generator = llm_service.stream_chat_completion(
                    messages=messages,
                    model=model,  # Pass custom model
                    max_tokens=max_tokens,
                    temperature=0.7
                )
                state["step_results"]["llm_stream"] = stream_generator
                logger.info(f"LLM streaming call prepared with model: {model or 'default'}")
            else:
                # For non-streaming: get the complete response
                llm_response = await llm_service.chat_completion(
                    messages=messages,
                    model=model,  # Pass custom model
                    max_tokens=max_tokens,
                    temperature=0.7
                )
                state["step_results"]["llm_response"] = llm_response
                logger.info(f"LLM call completed with model: {model or 'default'}")

        except Exception as e:
            state["error"] = f"LLM call failed: {str(e)}"
            logger.error(f"LLM call failed: {e}")

        return state

    async def _finalize_node(self, state: AgentWorkflowState) -> AgentWorkflowState:
        """Finalize and return raw LLM response (streaming or non-streaming)"""
        try:
            state["current_step"] = "finalize"
            step_results = state["step_results"]

            # Return appropriate response based on stream mode
            if step_results.get("is_stream", False):
                state["final_output"] = step_results.get("llm_stream")
            else:
                state["final_output"] = step_results.get("llm_response", {})

            logger.info("Content processing workflow completed")

        except Exception as e:
            state["error"] = f"Finalization failed: {str(e)}"
            logger.error(f"Finalization failed: {e}")

        return state



    # Direct access methods for API usage
    async def process_summary(self,
                              content: Optional[str] = None,
                              messages: Optional[List[Dict[str, str]]] = None,
                              model: Optional[str] = None,
                              stream: bool = False,
                              max_tokens: Optional[int] = None,
                              history_strategy: str = "truncate") -> Union[Any, AsyncGenerator[str, None]]:
        """Summary/chat processing with smart context management"""
        
        # Prepare input data for LangGraph workflow
        input_data = {
            "content": content,
            "messages": messages or [],
            "model": model,
            "stream": stream,
            "max_tokens": max_tokens or settings.default_max_tokens,
            "history_strategy": history_strategy,
        }

        # Execute the LangGraph workflow
        agent_state = await self.execute(input_data)
        
        if agent_state.status == TaskStatus.FAILED:
            # Return error in appropriate format
            error_response = {"error": agent_state.error_message}
            if stream:
                async def error_stream():
                    yield f"data: {json.dumps(error_response)}"
                return error_stream()
            else:
                return error_response

        # Return the result from workflow (either stream generator or response data)
        return agent_state.output_data

    
