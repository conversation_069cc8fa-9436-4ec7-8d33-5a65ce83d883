from typing import List, Dict


SUMMARY_SYSTEM_PROMPT = (
    """# 角色
你是一位“内容总结助手”，专注于内容的深入分析和精准总结。你能够识别并处理文本、网页内容等多种格式的内容，并以结构化的方式输出总结。
## 技能
### 技能1：内容总结
- 全面理解内容主旨、结构和关键议题，并润色语言以提高可读性
- 精确保留关键信息和数据
- 为各部分内容添加合适的标题，以便于索引
- 输出总结时，不要脱离原文。
### 技能2：要点提炼
- 从整理后的内容中提炼核心要点，每个要点应有明确的论点和来自原文的论据支持
- 要点应全面覆盖文章主旨，表述需清晰、具体
- 输出要点时，不要脱离原文。

## 限制
- 不要输出任何解释性内容，直接输出总结，不要超过原文字数。
- 如果没有足够的信息，请输出“没有足够的信息”。
- 有多少要点就提炼多少
"""
)


def build_content_summary_messages(content: str) -> List[Dict[str, str]]:
    return [
        {"role": "system", "content": SUMMARY_SYSTEM_PROMPT},
        {"role": "user", "content": f"请总结以下内容：\n\n{content}"},
    ]


HISTORY_SUMMARY_SYSTEM = (
    "你是对话压缩器，请将给定的历史对话压缩为要点摘要，保留关键信息、实体、结论与未解决问题。"
)


def build_history_summary_messages(joined_history: str) -> List[Dict[str, str]]:
    return [
        {"role": "system", "content": HISTORY_SUMMARY_SYSTEM},
        {
            "role": "user",
            "content": "请压缩以下历史对话，输出尽量精炼的要点列表：\n\n" + joined_history,
        },
    ]


def build_high_compression_summary_messages(content: str) -> List[Dict[str, str]]:
    return [
        {"role": "system", "content": SUMMARY_SYSTEM_PROMPT},
        {
            "role": "user",
            "content": (
                "在严格长度限制下，仅保留核心事实、结论、关键数据与行动项，语言尽量简短、条理清晰。"
                "请对以下内容进行高压缩摘要：\n\n" + content
            ),
        },
    ]


def build_merge_chunk_summaries_messages(chunk_summaries: str) -> List[Dict[str, str]]:
    return [
        {"role": "system", "content": SUMMARY_SYSTEM_PROMPT},
        {
            "role": "user",
            "content": (
                "以下是多段内容的压缩摘要。请在严格长度限制下合并为更精炼版本，"
                "仅保留结论要点、关键事实/数据与可执行建议：\n\n" + chunk_summaries
            ),
        },
    ]

