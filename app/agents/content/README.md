### 能力概览

- **内容总结与要点提炼**：
  - 全面理解主旨、结构、议题；按层次输出总结；从总结中提炼关键要点，覆盖文章主旨，数量不做上限强约束。
- **上下文预算管控**：
  - 结合目标模型的上下文窗口与预期输出上限 `max_tokens`，对输入消息进行 token 级的预算与裁剪，保留最新意图。
- **长内容处理（保序流式友好）**：
  - 超过预算时，对“前序内容”做高压缩摘要并合并；将合并摘要与“最近片段全文”拼接，使最终调用仍可流式输出。
- **对话历史处理两策略**：
  - summarize：保留对话尾巴 + 头部历史摘要插入为系统消息。
  - truncate：不调用 LLM，直接基于 token 预算从末尾截取。
- **多输出形态**：
  - 非流式：原样透传上游 JSON/文本；流式：原样透传 SSE 行。
- **容错与回退**：
  - 中间摘要失败时回退到朴素截断；流式失败时以一行错误 JSON 提示。

---

### LangGraph 工作流设计

- **入口与路由**：
  - 入口节点为 `validate_input`，负责校验与归一化输入；随后根据 `has_content` 与 `history_strategy` 分流：
    - 有内容 → `build_from_content`
    - 无内容且策略为 summarize → `compact_history`
    - 否则 → `truncate_history`

- **主干流程**：
  - 预处理节点（`build_from_content`/`compact_history`/`truncate_history`）统一产出 `processed_messages` → `call_llm` → `finalize`。

- **节点列表与职责**：
  - `validate_input`：
    - 校验 `content` 或 `messages` 至少一项有效；解析 `model/stream/max_tokens/history_strategy`。
    - 产出：`has_content`、`messages`、`content`、`max_tokens`、`model`、`is_stream` 等。
  - `build_from_content`（内容路径）：
    - 计算预算：`allowed_input_tokens = max(2000, context_window - max_tokens - 300)`。
    - 若内容超预算：
      - 切块 → 并发压缩前 N-1 块 → 合并为前序摘要 → 与“最近片段全文”拼接；超预算时优先裁剪前序摘要。
    - 生成用于总结的消息集合，并估算 `prompt_tokens`。
  - `compact_history`（历史智能压缩）：
    - 预算：`allowed_input_tokens = max(1000, context_window - max_tokens - 300)`。
    - 从末尾尽量装入预算，确保包含“最近一次用户消息”；对“头部历史”做摘要并插入为系统消息；若仍超预算，优先截断摘要。
    - 产出压缩后的 `processed_messages` 与 `prompt_tokens`。
  - `truncate_history`（历史直接截断）：
    - 不调用 LLM，仅基于预算从末尾截取，尽量保留“最近用户消息”。
    - 产出截断后的 `processed_messages` 与 `prompt_tokens`。
  - `call_llm`：
    - 使用 `processed_messages` 调用模型；
    - 非流式得到 `llm_response`；流式得到 `llm_stream`（逐行 SSE）。
  - `finalize`：
    - 根据 `is_stream` 返回 `llm_stream` 或 `llm_response` 作为最终输出。

- **路由规则（简述）**：
  - `has_content == true` → 内容路径；否则根据 `history_strategy`：`summarize` → 智能压缩；`truncate` → 直接截断。

---

### 关键参数与默认约束

- 安全边界 `safety_margin = 300`（为对话/内容输入预留，避免越界）。
- 内容路径最低输入预算下限：`2000`；历史路径预算下限：`1000`。
- 消息 token 估算包含固定额外开销（经验值 8/条），在不同模型上偏保守。
- 生成上限 `max_tokens` 默认取自配置，可外部覆盖。

---

### 返回与错误

- 非流式：透传上游 JSON/文本；失败返回 `{error: ...}`。
- 流式：透传上游 SSE 行；失败返回仅一行 `data: {"error": ...}`，便于前端快速消费。

