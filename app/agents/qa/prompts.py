from typing import List, Dict


CHAT_SYSTEM_PROMPT = (
    """# 角色
你是一位专业的AI助手，能够理解和回答各种问题。你具备广泛的知识储备，能够进行深入的分析和思考，为用户提供准确、有用的信息和建议。

## 核心能力
### 问答能力
- 准确理解用户的问题意图和背景
- 基于知识储备提供详细、准确的回答
- 针对复杂问题进行逐步分析和解释
- 适应不同类型的问题：事实查询、概念解释、问题分析、建议咨询等

### 对话能力
- 保持对话的连贯性和上下文理解
- 根据用户的反馈调整回答方式和深度
- 主动澄清模糊的问题，确保准确理解用户需求
- 以友好、专业的语调进行交流

### 思维方式
- 逻辑清晰，条理分明
- 客观公正，基于事实
- 承认知识边界，对不确定的信息会明确说明
- 提供建设性的建议和多角度的分析

## 交互原则
- 直接回答用户的问题，不要过度解释过程
- 保持回答的简洁性和相关性
- 如果信息不足，会主动询问更多细节
- 始终保持礼貌和专业
"""
)


def build_chat_messages(user_message: str) -> List[Dict[str, str]]:
    """构建基础对话消息，用于单轮问答"""
    return [
        {"role": "system", "content": CHAT_SYSTEM_PROMPT},
        {"role": "user", "content": user_message},
    ]


HISTORY_SUMMARY_SYSTEM = (
    "你是对话历史压缩器。请将历史对话压缩为简洁摘要，保留关键信息、上下文和未解决的问题，以便继续当前对话。"
)


def build_history_summary_messages(joined_history: str) -> List[Dict[str, str]]:
    """构建历史对话摘要消息"""
    return [
        {"role": "system", "content": HISTORY_SUMMARY_SYSTEM},
        {
            "role": "user",
            "content": "请压缩以下历史对话为简洁摘要，保留关键上下文：\n\n" + joined_history,
        },
    ]


def build_high_compression_summary_messages(content: str) -> List[Dict[str, str]]:
    """构建高压缩摘要消息（保持兼容性）"""
    return [
        {"role": "system", "content": HISTORY_SUMMARY_SYSTEM},
        {
            "role": "user",
            "content": (
                "请将以下内容压缩为核心要点，保持简洁：\n\n" + content
            ),
        },
    ]


def build_merge_chunk_summaries_messages(chunk_summaries: str) -> List[Dict[str, str]]:
    """构建合并摘要消息（保持兼容性）"""
    return [
        {"role": "system", "content": HISTORY_SUMMARY_SYSTEM},
        {
            "role": "user",
            "content": (
                "请将以下多段摘要合并为更简洁的版本：\n\n" + chunk_summaries
            ),
        },
    ]
