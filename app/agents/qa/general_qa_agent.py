"""
General Q&A agent with LangGraph workflow - direct LLM passthrough
"""
from typing import Optional, AsyncGenerator, Union, Dict, Any, List
import json
import time

from langgraph.graph import StateGraph, END

from app.agents.base import BaseAgent, AgentWorkflowState
from app.core.models import AgentType, TaskStatus
from app.services.llm_service import llm_service
from app.services.web_search_service import web_search_service
from app.tools.search_tools import get_tool_schemas, execute_tool
from app.utils.logger import get_logger
from app.config import settings
from app.agents.content.context_utils import (
    estimate_messages_tokens,
    fit_messages_into_budget,
    truncate_messages_into_budget,
)
from app.agents.qa.prompts import (
    CHAT_SYSTEM_PROMPT,
    build_chat_messages
)

logger = get_logger(__name__)


class GeneralQAAgent(BaseAgent):
    """General Q&A agent with conversation history management and LLM passthrough"""

    def __init__(self, agent_type: AgentType):
        super().__init__(
            agent_type=agent_type,
            name="General Q&A Agent",
            description="Handles general Q&A with conversation history support and returns raw LLM responses"
        )

    def _build_graph(self):
        """Build LangGraph workflow for Q&A processing"""
        workflow = StateGraph(AgentWorkflowState)

        # Add processing nodes
        workflow.add_node("validate_input", self._validate_input_node)
        workflow.add_node("truncate_history", self._truncate_history_node)
        workflow.add_node("call_llm_with_tools", self._call_llm_with_tools_node)
        workflow.add_node("execute_tools", self._execute_tools_node)
        workflow.add_node("finalize", self._finalize_node)

        # Define workflow edges
        workflow.set_entry_point("validate_input")
        # route based on input type
        workflow.add_conditional_edges(
            "validate_input",
            self._route_after_validation,
            {
                "truncate_history": "truncate_history",
            },
        )
        
        # 所有路径都通向 LLM 调用（带工具支持）
        workflow.add_edge("truncate_history", "call_llm_with_tools")
        
        # LLM 调用后的路由：检查是否需要执行工具
        workflow.add_conditional_edges(
            "call_llm_with_tools",
            self._route_after_llm_call,
            {
                "execute_tools": "execute_tools",
                "finalize": "finalize"
            }
        )
        
        # 工具执行后回到 LLM 调用（用于处理工具结果）
        workflow.add_edge("execute_tools", "call_llm_with_tools")
        workflow.add_edge("finalize", END)

        # Compile the graph
        self.graph = workflow.compile()
        logger.info("LangGraph workflow built for Q&A agent")

    async def process_node(self, state: AgentWorkflowState) -> AgentWorkflowState:
        """Required by BaseAgent interface"""
        return state

    async def _validate_input_node(self, state: AgentWorkflowState) -> AgentWorkflowState:
        """Validate and prepare input data for Q&A"""
        try:
            state["current_step"] = "validate_input"
            input_data = state["input_data"]

            messages = input_data.get("messages", [])
            model = input_data.get("model")
            is_stream = input_data.get("stream", False)
            max_tokens = input_data.get("max_tokens") or settings.default_max_tokens

            # Validate messages is not empty
            if not messages or len(messages) == 0:
                raise ValueError("messages must be a non-empty list")

            # Store validated data
            state["step_results"]["messages"] = messages
            state["step_results"]["model"] = model
            state["step_results"]["is_stream"] = is_stream
            state["step_results"]["max_tokens"] = max_tokens
            
            # Handle search options - 保存搜索配置供工具使用
            search_options = input_data.get("searchOptions")
            state["step_results"]["search_options"] = search_options

            logger.info(f"Input validated - processing conversation with {len(messages)} messages")

        except Exception as e:
            state["error"] = f"Input validation failed: {str(e)}"
            logger.error(f"Input validation failed: {e}")

        return state






    async def _truncate_history_node(self, state: AgentWorkflowState) -> AgentWorkflowState:
        """Truncate history by tokens and ensure system prompt is present"""
        try:
            state["current_step"] = "truncate_history"
            step_results = state["step_results"]
            max_tokens = step_results.get("max_tokens", settings.default_max_tokens)
            model = step_results.get("model")
            incoming_messages = step_results.get("messages", [])
            search_options = step_results.get("search_options")

            # Check if system message exists
            has_system_message = incoming_messages and incoming_messages[0].get("role") == "system"
            
            # 搜索工具使用指导
            search_tool_guidance = ""
            if search_options and search_options.get("enableInternetSearch", False):
                search_tool_guidance = """

## 🔧 工具使用能力

你现在可以使用以下工具进行深度研究：

### web_search 工具
- **用途**：获取最新信息、实时数据、具体事实
- **何时使用**：
  - 用户询问最新事件、新闻、趋势
  - 需要具体产品信息、价格、规格
  - 需要实时数据（天气、股价等）
  - 用户明确要求搜索信息
  - 你的知识可能过时或不够准确时

### 🎯 使用原则
1. **智能判断**：先考虑是否需要搜索最新信息
2. **精准搜索**：选择最相关的关键词，避免过长的句子
3. **多轮研究**：根据初步结果决定是否需要进一步搜索
4. **综合分析**：基于搜索结果和你的知识提供全面回答
5. **引用来源**：在回答中适当引用搜索结果

请根据用户问题智能判断是否需要使用搜索工具，并提供最准确、最有用的回答。"""
            
            if has_system_message:
                # 如果用户提供了system message，在其后追加搜索工具指导
                system_message = incoming_messages[0]
                enhanced_content = system_message["content"] + search_tool_guidance
                incoming_messages[0] = {"role": "system", "content": enhanced_content}
            else:
                # 如果用户没有提供system message，添加完整的增强system prompt
                enhanced_system_prompt = CHAT_SYSTEM_PROMPT + search_tool_guidance
                incoming_messages = [{"role": "system", "content": enhanced_system_prompt}] + incoming_messages

            context_window = settings.get_context_window(model)
            safety_margin = 300
            allowed_input_tokens = max(1000, context_window - max_tokens - safety_margin)

            processed_messages, token_count = truncate_messages_into_budget(
                incoming_messages, allowed_input_tokens
            )
            state["step_results"]["processed_messages"] = processed_messages
            state["step_results"]["prompt_tokens"] = token_count
            
            search_status = "启用搜索工具" if search_options and search_options.get("enableInternetSearch", False) else "普通对话模式"
            logger.info(
                f"Messages processing completed - {search_status}, {len(processed_messages)} messages, tokens≈{token_count}"
            )
        except Exception as e:
            state["error"] = f"Truncate history failed: {str(e)}"
            logger.error(f"Truncate history failed: {e}")
        return state

    def _route_after_validation(self, state: AgentWorkflowState) -> str:
        """Route based on input type - simplified for tool-based approach"""
        # All inputs are now messages, so we always use truncate_history
        return "truncate_history"

    async def _call_llm_with_tools_node(self, state: AgentWorkflowState) -> AgentWorkflowState:
        """调用 LLM 并支持工具调用"""
        try:
            state["current_step"] = "call_llm_with_tools"
            step_results = state["step_results"]
            
            messages = step_results.get("processed_messages", [])
            model = step_results.get("model")
            is_stream = step_results.get("is_stream", False)
            max_tokens = step_results.get("max_tokens", settings.default_max_tokens)
            
            # 检查是否启用搜索工具
            search_options = step_results.get("search_options")
            tools = None
            
            if search_options and search_options.get("enableInternetSearch", False):
                tools = get_tool_schemas()
                logger.info("🔧 启用工具调用模式，LLM 可以调用搜索工具")
            else:
                logger.info("💬 普通对话模式，不启用工具调用")
            
            # 调用LLM
            logger.info(f"🤖 调用 LLM: model={model or 'default'}, tools={'enabled' if tools else 'disabled'}")
            
            if is_stream:
                # 流式调用 - 注意：工具调用可能不支持流式
                stream_generator = llm_service.stream_chat_completion(
                    messages=messages,
                    model=model,
                    max_tokens=max_tokens,
                    temperature=0.7,
                    tools=tools if not tools else None  # 流式模式可能不支持工具
                )
                state["step_results"]["llm_stream"] = stream_generator
                logger.info("📡 流式响应已启动")
                llm_response = None  # 流式模式下没有立即响应
            else:
                # 非流式调用，支持工具
                llm_response = await llm_service.chat_completion(
                    messages=messages,
                    model=model,
                    max_tokens=max_tokens,
                    temperature=0.7,
                    tools=tools
                )
                state["step_results"]["llm_response"] = llm_response
                
                            # 检查是否有工具调用（仅在非流式模式下）
            if llm_response and "choices" in llm_response:
                choice = llm_response["choices"][0]
                message = choice.get("message", {})
                tool_calls = message.get("tool_calls")
                
                if tool_calls:
                    state["step_results"]["tool_calls"] = tool_calls
                    state["step_results"]["assistant_message"] = message
                    logger.info(f"🔨 LLM 请求调用 {len(tool_calls)} 个工具")
                else:
                    logger.info("✅ LLM 直接回答，无需工具调用")
                        
                logger.info(f"LLM call completed with model: {model or 'default'}")
            
        except Exception as e:
            state["error"] = f"LLM call failed: {str(e)}"
            logger.error(f"LLM call failed: {e}")
            
        return state

    def _route_after_llm_call(self, state: AgentWorkflowState) -> str:
        """LLM 调用后的路由：检查是否需要执行工具"""
        step_results = state.get("step_results", {})
        
        # 检查是否有工具调用请求（且非流式）
        if step_results.get("tool_calls") and not step_results.get("is_stream", False):
            return "execute_tools"
        else:
            return "finalize"

    async def _execute_tools_node(self, state: AgentWorkflowState) -> AgentWorkflowState:
        """执行 LLM 请求的工具"""
        try:
            state["current_step"] = "execute_tools"
            step_results = state["step_results"]
            tool_calls = step_results.get("tool_calls", [])
            search_options = step_results.get("search_options", {})
            
            logger.info(f"⚡ 开始执行 {len(tool_calls)} 个工具调用")
            
            tool_results = []
            for i, tool_call in enumerate(tool_calls, 1):
                function = tool_call.get("function", {})
                tool_name = function.get("name")
                tool_args_str = function.get("arguments", "{}")
                tool_call_id = tool_call.get("id", "")
                
                try:
                    tool_args = json.loads(tool_args_str)
                    logger.info(f"🔧 执行工具 {i}/{len(tool_calls)}: {tool_name}")
                    logger.info(f"   参数: {tool_args}")
                    
                    # 为搜索工具添加搜索配置
                    if tool_name == "web_search":
                        tool_args["search_options"] = search_options
                    
                    # 执行工具
                    result = await execute_tool(tool_name, **tool_args)
                    
                    tool_results.append({
                        "tool_call_id": tool_call_id,
                        "role": "tool",
                        "name": tool_name,
                        "content": json.dumps(result, ensure_ascii=False, indent=2)
                    })
                    
                    if result.get("success"):
                        logger.info(f"✅ 工具 {tool_name} 执行成功")
                    else:
                        logger.warning(f"⚠️ 工具 {tool_name} 执行完成但有警告")
                    
                except Exception as e:
                    error_result = {
                        "success": False,
                        "error": f"工具执行失败: {str(e)}"
                    }
                    tool_results.append({
                        "tool_call_id": tool_call_id,
                        "role": "tool", 
                        "name": tool_name,
                        "content": json.dumps(error_result, ensure_ascii=False)
                    })
                    logger.error(f"❌ 工具 {tool_name} 执行失败: {e}")
            
            # 将工具结果添加到消息历史
            messages = step_results.get("processed_messages", [])
            assistant_message = step_results.get("assistant_message")
            
            if assistant_message:
                messages.append(assistant_message)
            
            messages.extend(tool_results)
            state["step_results"]["processed_messages"] = messages
            state["step_results"]["tool_results"] = tool_results
            
            # 清除工具调用状态，准备下一轮LLM调用
            state["step_results"]["tool_calls"] = None
            state["step_results"]["assistant_message"] = None
            
            logger.info(f"🎯 所有工具执行完成，共 {len(tool_results)} 个结果")
            
        except Exception as e:
            state["error"] = f"工具执行失败: {str(e)}"
            logger.error(f"工具执行失败: {e}")
            
        return state

    async def _call_llm_node(self, state: AgentWorkflowState) -> AgentWorkflowState:
        """Call LLM and store response (streaming or non-streaming)"""
        try:
            state["current_step"] = "call_llm"
            step_results = state["step_results"]

            messages = step_results.get("processed_messages", [])
            model = step_results.get("model")  # Get custom model
            is_stream = step_results.get("is_stream", False)
            max_tokens = step_results.get("max_tokens", settings.default_max_tokens)

            if is_stream:
                # For streaming: create and store the stream generator
                stream_generator = llm_service.stream_chat_completion(
                    messages=messages,
                    model=model,  # Pass custom model
                    max_tokens=max_tokens,
                    temperature=0.7
                )
                state["step_results"]["llm_stream"] = stream_generator
                logger.info(f"LLM streaming call prepared with model: {model or 'default'}")
            else:
                # For non-streaming: get the complete response
                llm_response = await llm_service.chat_completion(
                    messages=messages,
                    model=model,  # Pass custom model
                    max_tokens=max_tokens,
                    temperature=0.7
                )
                state["step_results"]["llm_response"] = llm_response
                logger.info(f"LLM call completed with model: {model or 'default'}")

        except Exception as e:
            state["error"] = f"LLM call failed: {str(e)}"
            logger.error(f"LLM call failed: {e}")

        return state

    async def _finalize_node(self, state: AgentWorkflowState) -> AgentWorkflowState:
        """Finalize and return raw LLM response (streaming or non-streaming)"""
        try:
            state["current_step"] = "finalize"
            step_results = state["step_results"]

            # Return appropriate response based on stream mode
            if step_results.get("is_stream", False):
                state["final_output"] = step_results.get("llm_stream")
            else:
                state["final_output"] = step_results.get("llm_response", {})

            logger.info("Content processing workflow completed")

        except Exception as e:
            state["error"] = f"Finalization failed: {str(e)}"
            logger.error(f"Finalization failed: {e}")

        return state



    # Direct access methods for API usage
    async def process_chat(self,
                           messages: List[Dict[str, str]],
                           model: Optional[str] = None,
                           stream: bool = False,
                           max_tokens: Optional[int] = None,
                           searchOptions: Optional[Dict[str, Any]] = None) -> Union[Any, AsyncGenerator[str, None]]:
        """Q&A processing with smart conversation history management"""
        
        # Prepare input data for LangGraph workflow
        input_data = {
            "messages": messages,
            "model": model,
            "stream": stream,
            "max_tokens": max_tokens or settings.default_max_tokens,
            "searchOptions": searchOptions,
        }

        # Execute the LangGraph workflow
        agent_state = await self.execute(input_data)
        
        if agent_state.status == TaskStatus.FAILED:
            # Return error in appropriate format
            error_response = {"error": agent_state.error_message}
            if stream:
                async def error_stream():
                    yield f"data: {json.dumps(error_response)}"
                return error_stream()
            else:
                return error_response

        # Return the result from workflow (either stream generator or response data)
        return agent_state.output_data
