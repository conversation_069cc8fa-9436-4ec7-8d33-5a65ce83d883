"""
LangGraph agent base class
"""
from typing import Dict, Any, Optional, List, Type, TypedDict
from abc import ABC, abstractmethod
import uuid

from app.core.models import AgentState, AgentType, TaskStatus
from app.utils.logger import get_logger

logger = get_logger(__name__)


class AgentWorkflowState(TypedDict):
    """LangGraph workflow state"""
    input_data: Dict[str, Any]
    current_step: str
    step_results: Dict[str, Any]
    final_output: Optional[Any]
    error: Optional[str]
    metadata: Dict[str, Any]


class BaseAgent(ABC):
    """LangGraph-based agent base class"""

    def __init__(self, agent_type: AgentType, name: str, description: str = ""):
        self.agent_type = agent_type
        self.name = name
        self.description = description
        self.agent_id = str(uuid.uuid4())
        self.graph = None
        self._build_graph()

    @abstractmethod
    def _build_graph(self):
        """Build LangGraph workflow"""
        pass

    @abstractmethod
    async def process_node(self, state: AgentWorkflowState) -> AgentWorkflowState:
        """Process node logic"""
        pass

    async def execute(self, input_data: Dict[str, Any]) -> AgentState:
        """Execute agent workflow and return raw LLM response"""
        agent_state = AgentState(
            agent_id=self.agent_id,
            agent_type=self.agent_type,
            status=TaskStatus.RUNNING,
            input_data=input_data
        )

        try:
            logger.info(f"Executing agent: {self.name}")

            # Prepare LangGraph state
            workflow_state: AgentWorkflowState = {
                "input_data": input_data,
                "current_step": "start",
                "step_results": {},
                "final_output": None,
                "error": None,
                "metadata": {"agent_id": self.agent_id, "agent_name": self.name}
            }

            # Execute LangGraph workflow
            if self.graph:
                result = await self.graph.ainvoke(workflow_state)

                if result.get("error"):
                    raise Exception(result["error"])

                # Return raw LLM response directly
                output_data = result.get("final_output", {})
                agent_state.update_status(TaskStatus.COMPLETED, output_data)
                
                # Save workflow state for potential later access (e.g., for stream link appending)
                agent_state._workflow_state = result
            else:
                raise Exception("LangGraph workflow not initialized")

            logger.info(f"Agent completed: {self.name}")

        except Exception as e:
            error_message = f"Agent execution failed: {str(e)}"
            agent_state.update_status(TaskStatus.FAILED, error_message=error_message)
            logger.error(f"Agent failed: {self.name} - {error_message}")

        return agent_state

    def get_info(self) -> Dict[str, Any]:
        """Get agent info"""
        return {
            "agent_id": self.agent_id,
            "agent_type": self.agent_type.value,
            "name": self.name,
            "description": self.description,
            "framework": "LangGraph"
        }


class AgentRegistry:
    """Agent registry for managing agent types"""
    
    def __init__(self):
        self._agents: Dict[str, Type[BaseAgent]] = {}
        self._instances: Dict[str, BaseAgent] = {}
    
    def register(self, agent_type: AgentType, agent_class: Type[BaseAgent]):
        """Register agent class"""
        self._agents[agent_type.value] = agent_class
        logger.info(f"Registered agent: {agent_type.value} -> {agent_class.__name__}")
    
    def create_agent(self, agent_type: AgentType, **kwargs) -> BaseAgent:
        """Create agent instance"""
        agent_class = self._agents.get(agent_type.value)
        if not agent_class:
            raise ValueError(f"Agent type not found: {agent_type.value}")
        
        instance_key = f"{agent_type.value}_{id(kwargs)}"
        if instance_key not in self._instances:
            self._instances[instance_key] = agent_class(agent_type, **kwargs)
        
        return self._instances[instance_key]
    
    def get_agent_types(self) -> List[str]:
        """Get all registered agent types"""
        return list(self._agents.keys())


# Global agent registry
agent_registry = AgentRegistry()
