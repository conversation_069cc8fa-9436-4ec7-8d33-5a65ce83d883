"""
内容总结API - LangGraph agent with direct LLM passthrough
"""
from fastapi import APIRouter
from fastapi.responses import JSONResponse, StreamingResponse

from app.agents.base import agent_registry
from app.api.models import ContentSummaryRequest
from app.core.models import AgentType
from app.utils.logger import get_logger

logger = get_logger(__name__)
router = APIRouter()


@router.post("/summarize", 
             summary="Content Summarization",
             description="Summarize provided content or chat history with smart context management; returns raw LLM API format.",)
async def summarize_content(request: ContentSummaryRequest):
    """Content summarization with LangGraph processing and raw LLM passthrough"""
    logger.info(f"Processing content summarization request")

    # Create content agent
    agent = agent_registry.create_agent(AgentType.CONTENT_UNDERSTANDING)
    
    print("request", request)

    # Use agent's direct processing method
    result = await agent.process_summary(
        content=request.content,
        messages=request.messages,
        model=request.model,
        stream=request.stream,
        max_tokens=None,
        history_strategy=request.history_strategy
    )

    if request.stream:
        # Return raw LLM stream
        async def stream_generator():
            async for line in result:
                yield f"{line}\n\n"
        
        return StreamingResponse(
            stream_generator(),
            media_type="text/event-stream",
            headers={
                "Cache-Control": "no-cache",
                "Connection": "keep-alive",
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Headers": "*"
            }
        )
    else:
        # Return raw LLM response
        return JSONResponse(status_code=200, content=result)

