"""
翻译API - LangGraph agent with direct result passthrough
"""
from fastapi import APIRouter
from fastapi.responses import JSONResponse, StreamingResponse

from app.agents.base import agent_registry
from app.api.models import TranslationRequest
from app.core.models import AgentType
from app.utils.logger import get_logger

logger = get_logger(__name__)
router = APIRouter()


@router.post("/translate", 
             summary="Text Translation",
             description="Translate text using multiple providers (Doubao or LLM); supports streaming and non-streaming modes.")
async def translate_text(request: TranslationRequest):
    """Text translation with LangGraph processing and direct result passthrough"""
    logger.info(f"Processing translation request: provider={request.provider}, texts={len(request.question)}, stream={request.stream}")

    # Create translation agent
    agent = agent_registry.create_agent(AgentType.TRANSLATION)
    
    # Convert request to agent input format
    translate_options = None
    if request.translateOptions:
        translate_options = {
            "src_lang": request.translateOptions.src_lang,
            "tgt_lang": request.translateOptions.tgt_lang
        }
    
    # Use agent's direct processing method
    result = await agent.process_translation(
        question=request.question,
        stream=request.stream,
        translate_options=translate_options,
        provider=request.provider
    )

    if request.stream:
        # Return raw stream
        async def stream_generator():
            async for line in result:
                yield line
        
        return StreamingResponse(
            stream_generator(),
            media_type="text/event-stream",
            headers={
                "Cache-Control": "no-cache",
                "Connection": "keep-alive",
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Headers": "*"
            }
        )
    else:
        # Return raw result
        return JSONResponse(status_code=200, content=result)
