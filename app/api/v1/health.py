"""
健康检查API
"""
import time
import psutil
from datetime import datetime
from typing import Dict, Any
from fastapi import APIRouter, Depends
from pydantic import BaseModel

from app.config import settings
from app.core.models import HealthCheck
from app.services.llm_service import llm_service
from app.utils.logger import get_logger

logger = get_logger(__name__)
router = APIRouter()

# 服务启动时间
START_TIME = time.time()


class SystemInfo(BaseModel):
    """系统信息"""
    cpu_percent: float
    memory_percent: float
    disk_percent: float
    uptime: float


@router.get("/health", response_model=HealthCheck)
async def health_check():
    """基础健康检查"""
    return HealthCheck(
        status="healthy",
        version=settings.app_version,
        uptime=time.time() - START_TIME
    )


