"""
搜索工具定义
为 LLM 提供联网搜索能力
"""
import json
import asyncio
from typing import Dict, Any, List, Optional
from app.services.web_search_service import web_search_service
from app.utils.logger import logger

# 搜索工具的 Function Schema（OpenAI Function Calling 格式）
SEARCH_TOOL_SCHEMA = {
    "type": "function",
    "function": {
        "name": "web_search",
        "description": "联网搜索工具。当需要获取最新信息、实时数据、具体事实等时使用。可以搜索新闻、产品信息、技术资料等。支持多个搜索引擎。",
        "parameters": {
            "type": "object",
            "properties": {
                "query": {
                    "type": "string",
                    "description": "搜索关键词。请使用简洁、精准的关键词，避免过长的句子。例如：'iPhone 15 价格'、'2025年AI发展趋势'、'Python异步编程'"
                },
                "provider": {
                    "type": "array", 
                    "items": {"type": "string", "enum": ["hiagent", "wiki"]},
                    "description": "搜索引擎提供商。hiagent=联网搜索，wiki=Wiki搜索。默认使用hiagent",
                    "default": ["hiagent"]
                },
                "max_results": {
                    "type": "integer",
                    "description": "最大返回结果数，1-20之间，默认10",
                    "minimum": 1,
                    "maximum": 20,
                    "default": 10
                }
            },
            "required": ["query"]
        }
    }
}

class SearchToolExecutor:
    """搜索工具执行器"""
    
    def __init__(self):
        self.search_service = web_search_service
    
    async def execute_search(
        self, 
        query: str, 
        provider: List[str] = None,
        max_results: int = 10,
        search_options: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """
        执行搜索工具
        
        Args:
            query: 搜索关键词
            provider: 搜索提供商列表
            max_results: 最大结果数
            search_options: 原始搜索配置（用于获取认证信息等）
        
        Returns:
            搜索结果的标准格式
        """
        try:
            logger.info(f"执行搜索工具: query='{query}', provider={provider}, max_results={max_results}")
            
            # 设置默认值
            if provider is None:
                provider = ["hiagent"]
            
            # 构建搜索参数
            search_params = {
                "query": query,
                "provider": provider,
                "snippet": False  # 获取完整内容
            }
            
            # 如果有原始搜索配置，提取认证信息
            if search_options and "authInfos" in search_options:
                search_params["authInfos"] = search_options["authInfos"]
            
            # 执行搜索
            results = await self.search_service.search(**search_params)
            
            # 格式化结果为工具返回格式
            if results:
                formatted_results = []
                for result in results[:max_results]:
                    formatted_results.append({
                        "title": result.title,
                        "content": result.content[:500] + "..." if len(result.content) > 500 else result.content,  # 限制内容长度
                        "url": result.url,
                        "source": result.source
                    })
                
                tool_result = {
                    "success": True,
                    "results_count": len(formatted_results),
                    "results": formatted_results,
                    "search_summary": f"找到 {len(formatted_results)} 条关于 '{query}' 的搜索结果"
                }
                
                logger.info(f"搜索工具执行成功: 找到 {len(formatted_results)} 条结果")
                return tool_result
            else:
                tool_result = {
                    "success": False,
                    "results_count": 0,
                    "results": [],
                    "search_summary": f"未找到关于 '{query}' 的相关信息",
                    "error": "No results found"
                }
                
                logger.warning(f"搜索工具未找到结果: query='{query}'")
                return tool_result
                
        except Exception as e:
            error_msg = f"搜索工具执行失败: {str(e)}"
            logger.error(error_msg, exc_info=True)
            
            return {
                "success": False,
                "results_count": 0,
                "results": [],
                "search_summary": f"搜索 '{query}' 时出现错误",
                "error": error_msg
            }

# 全局搜索工具执行器实例
search_tool_executor = SearchToolExecutor()

# 工具注册表
AVAILABLE_TOOLS = {
    "web_search": {
        "schema": SEARCH_TOOL_SCHEMA,
        "executor": search_tool_executor.execute_search
    }
}

def get_tool_schemas() -> List[Dict[str, Any]]:
    """获取所有可用工具的 schema"""
    return [tool["schema"] for tool in AVAILABLE_TOOLS.values()]

async def execute_tool(tool_name: str, **kwargs) -> Dict[str, Any]:
    """
    执行指定的工具
    
    Args:
        tool_name: 工具名称
        **kwargs: 工具参数
    
    Returns:
        工具执行结果
    """
    if tool_name not in AVAILABLE_TOOLS:
        return {
            "success": False,
            "error": f"Unknown tool: {tool_name}",
            "available_tools": list(AVAILABLE_TOOLS.keys())
        }
    
    try:
        executor = AVAILABLE_TOOLS[tool_name]["executor"]
        result = await executor(**kwargs)
        return result
    except Exception as e:
        logger.error(f"Tool execution failed: {tool_name}, error: {str(e)}", exc_info=True)
        return {
            "success": False,
            "error": f"Tool execution failed: {str(e)}"
        }
