# 翻译API使用示例

## API端点
```
POST /api/v1/translate
```

## 请求格式

### 基本翻译请求
```json
{
    "question": [
        {"text": "Hello"},
        {"text": "World"}
    ],
    "stream": false,
    "translateOptions": {
        "src_lang": "en",
        "tgt_lang": "zh"
    },
    "provider": "doubao"
}
```

### 参数说明
- `question`: 待翻译文本数组，每个元素包含`text`字段
- `stream`: 是否使用流式响应（默认：false）
- `translateOptions`: 翻译选项
  - `src_lang`: 源语言代码（默认：en）
  - `tgt_lang`: 目标语言代码（默认：zh）
- `provider`: 翻译服务提供商（doubao 或 llm_translate，默认：doubao）

## 响应格式

### 成功响应
```json
{
    "code": "success",
    "message": "翻译成功",
    "data": [
        {
            "text": "Hello",
            "translateText": "你好"
        },
        {
            "text": "World",
            "translateText": "世界"
        }
    ]
}
```

### 错误响应
```json
{
    "code": "error",
    "message": "翻译失败的具体原因",
    "data": []
}
```

## 使用示例

### 1. 使用Doubao翻译（非流式）
```bash
curl -X POST "http://localhost:8000/api/v1/translate" \
  -H "Content-Type: application/json" \
  -d '{
    "question": [
        {"text": "We are extending Gemini to become a world model"},
        {"text": "that can make plans and imagine new experiences"}
    ],
    "stream": false,
    "translateOptions": {
        "src_lang": "en",
        "tgt_lang": "zh"
    },
    "provider": "doubao"
  }'
```

### 2. 使用LLM翻译（非流式）
```bash
curl -X POST "http://localhost:8000/api/v1/translate" \
  -H "Content-Type: application/json" \
  -d '{
    "question": [
        {"text": "Hello, how are you?"},
        {"text": "I am fine, thank you."}
    ],
    "stream": false,
    "translateOptions": {
        "src_lang": "en",
        "tgt_lang": "zh"
    },
    "provider": "llm_translate"
  }'
```

### 3. 流式翻译
```bash
curl -X POST "http://localhost:8000/api/v1/translate" \
  -H "Content-Type: application/json" \
  -d '{
    "question": [
        {"text": "This is a streaming translation test."}
    ],
    "stream": true,
    "translateOptions": {
        "src_lang": "en",
        "tgt_lang": "zh"
    },
    "provider": "llm_translate"
  }'
```

### 4. 中文到英文翻译
```bash
curl -X POST "http://localhost:8000/api/v1/translate" \
  -H "Content-Type: application/json" \
  -d '{
    "question": [
        {"text": "你好，世界！"},
        {"text": "这是一个翻译测试。"}
    ],
    "stream": false,
    "translateOptions": {
        "src_lang": "zh",
        "tgt_lang": "en"
    },
    "provider": "doubao"
  }'
```

## 支持的语言代码
- `en`: 英语
- `zh`: 中文
- `ja`: 日语
- `ko`: 韩语
- `fr`: 法语
- `de`: 德语
- `es`: 西班牙语
- `ru`: 俄语

## 错误处理
API会自动处理以下错误情况：
1. 输入验证错误
2. 翻译服务不可用
3. 网络超时
4. 自动回退到备用翻译服务

## 性能特性
- 支持批量翻译（一次请求多个文本）
- 自动选择最佳翻译服务
- 失败时自动回退到备用服务
- 支持流式和非流式响应模式
