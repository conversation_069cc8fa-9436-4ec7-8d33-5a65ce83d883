# 通用Agent联网查询实现方案技术文档

## 1. 概述

本文档详细分析了web-assistant-agents项目中通用agent的联网查询实现方案。该系统采用多层架构设计，支持智能搜索判断、多搜索源整合、工具调用机制等核心功能。

## 2. 整体架构

### 2.1 核心组件

```mermaid
graph TB
    A[用户请求] --> B[GeneralQAAgent]
    B --> C[智能搜索判断]
    C --> D{需要搜索?}
    D -->|是| E[WebSearchService]
    D -->|否| F[直接LLM处理]
    E --> G[WebSearchClient]
    G --> H[搜索API]
    H --> I[结果解析]
    I --> J[结果格式化]
    J --> K[整合到对话]
    K --> L[LLM生成回答]
    F --> L
    L --> M[返回结果]
```

### 2.2 模块关系

- **GeneralQAAgent**: 主控制器，负责整个对话流程
- **WebSearchService**: 搜索服务管理器，提供统一搜索接口
- **WebSearchClient**: HTTP客户端，负责与搜索API通信
- **SearchTools**: 工具调用模块，支持Function Calling
- **LLMService**: 大语言模型服务，用于智能判断和回答生成

## 3. 实现方案梳理

### 3.1 hiagent模块联网查询实现

#### 核心类：WebSearchClient

```python
class WebSearchClient:
    def __init__(self, api_url: str = "http://*************/fst/api/v1/websearch"):
        self.api_url = api_url
        self.session = None
        self._timeout = aiohttp.ClientTimeout(total=180)  # 3分钟超时
```

**关键特性：**
- 使用aiohttp异步HTTP客户端
- 支持会话复用和连接池
- 3分钟超时设置，适应长时间搜索需求
- 支持多种搜索提供商（hiagent、wiki）

#### 搜索请求构建

```python
def _build_payload(self, request: SearchRequest) -> Dict[str, Any]:
    payload = {
        "query": request.query,
        "provider": request.provider,
        "snippet": request.snippet
    }
    if request.authInfos:
        payload["authInfos"] = request.authInfos
    return payload
```

### 3.2 wiki相关模块联网查询方案

wiki搜索通过相同的WebSearchClient实现，但使用不同的provider参数：

- **provider="wiki"**: 搜索公司内部资料库
- **provider="hiagent"**: 搜索外网信息
- **provider=["wiki", "hiagent"]**: 同时搜索多个源

### 3.3 其他联网查询组件

#### 工具调用机制（SearchTools）

支持OpenAI Function Calling格式的工具调用：

```python
SEARCH_TOOL_SCHEMA = {
    "type": "function",
    "function": {
        "name": "web_search",
        "description": "智能搜索工具。根据查询内容选择合适的搜索源获取信息。",
        "parameters": {
            "type": "object",
            "properties": {
                "query": {"type": "string"},
                "provider": {
                    "type": "array",
                    "items": {"type": "string", "enum": ["hiagent", "wiki"]}
                }
            }
        }
    }
}
```

## 4. 技术细节分析

### 4.1 联网查询触发机制

#### 智能判断机制

系统采用LLM智能判断是否需要联网搜索：

```python
async def _analyze_search_need_with_llm(self, messages: list) -> tuple[bool, str, list]:
    judge_system_prompt = """你是一个搜索需求分析助手。分析用户的对话是否需要联网搜索。
    
    分析规则：
    1. 天气、股价、新闻等实时信息 → 需要搜索
    2. 包含"今天"、"最新"、"现在"等时间词 → 需要搜索  
    3. 公司内部术语、产品名称 → 需要搜索wiki
    4. 基础常识、创意任务 → 不需要搜索
    """
```

#### 触发条件

1. **用户显式启用**: `searchOptions.enableInternetSearch = true`
2. **智能判断触发**: LLM分析对话内容判断需要实时信息
3. **工具调用触发**: LLM主动调用搜索工具

### 4.2 网络请求库和API接口

#### HTTP客户端配置

```python
self._timeout = aiohttp.ClientTimeout(
    total=300,      # 总超时5分钟
    connect=30,     # 连接超时30秒  
    sock_read=120   # 读取超时2分钟
)
```

#### API接口规范

**请求格式：**
```json
{
    "query": "搜索关键词",
    "provider": ["hiagent", "wiki"],
    "snippet": false,
    "authInfos": {
        "wiki": {"token": "xxx"}
    }
}
```

**响应格式：**
```json
{
    "code": "0",
    "data": {
        "results": [
            {
                "title": "标题",
                "content": "内容",
                "url": "链接",
                "providerName": "hiagent"
            }
        ]
    }
}
```

### 4.3 查询参数构建和传递

#### 参数构建策略

1. **基础参数**: query（必需）、provider（默认hiagent）
2. **内容控制**: snippet（false获取完整内容）
3. **认证信息**: authInfos（支持多provider认证）
4. **结果限制**: max_results（默认10条）

#### 参数传递链路

```
API请求 → SearchOptions → SearchRequest → WebSearchClient → 搜索API
```

### 4.4 错误处理和重试机制

#### 错误处理层级

1. **网络层错误**: 连接超时、DNS解析失败
2. **HTTP层错误**: 4xx/5xx状态码
3. **数据层错误**: JSON解析失败、格式不匹配
4. **业务层错误**: 搜索结果为空、认证失败

#### 错误处理策略

```python
try:
    # 执行搜索
    results = await web_search_service.search(...)
except Exception as e:
    logger.error(f"搜索执行异常: {str(e)}", exc_info=True)
    # 搜索失败不影响正常对话，继续执行
    step_results["has_search_results"] = False
```

**特点：**
- 搜索失败不中断对话流程
- 详细的错误日志记录
- 优雅降级到无搜索模式

## 5. 数据处理流程

### 5.1 查询结果获取和解析

#### 结果解析逻辑

```python
def _parse_search_results(self, response: Dict[str, Any], provider) -> List[SearchResult]:
    results = []
    if "code" in response and response["code"] == "0":
        data = response.get("data", {})
        if isinstance(data, dict) and "results" in data:
            search_results = data["results"]
            for item in search_results:
                result = SearchResult(
                    title=item.get("title", ""),
                    content=item.get("content", ""),
                    url=item.get("url", ""),
                    source=item.get("providerName", str(provider))
                )
                results.append(result)
    return results
```

### 5.2 结果数据清洗和格式化

#### 文本清洗

```python
def _clean_text(self, text: str) -> str:
    if not text:
        return ""
    # 移除特殊字符，限制长度
    cleaned = re.sub(r'[\x00-\x1f\x7f-\x9f]', '', text)
    return cleaned[:2000] if len(cleaned) > 2000 else cleaned
```

#### 格式化输出

```python
def format_search_results(self, results: List[SearchResult], max_results: int = 5) -> str:
    # 按provider分类
    wiki_results = []
    web_results = []
    
    for result in results[:max_results]:
        if "wiki" in result.source.lower():
            wiki_results.append(result)
        else:
            web_results.append(result)
    
    # 分别格式化wiki和web结果
    formatted_sections = []
    if web_results:
        formatted_sections.append("=== 🌐 联网搜索结果 ===")
        # ... 格式化逻辑
```

### 5.3 多个查询结果整合策略

#### 结果优先级

1. **Wiki结果优先**: 内部资料优先展示
2. **相关性排序**: 按搜索API返回顺序
3. **数量限制**: 默认最多5条结果
4. **内容截断**: 单条结果最多500字符

#### 整合到对话上下文

```python
# 将搜索结果作为系统消息加入对话
search_message = {
    "role": "system", 
    "content": f"**实时搜索结果**（请基于以下最新信息回答用户问题）：\n\n{search_context}"
}
# 插入到最后一条用户消息前
messages.insert(-1, search_message)
```

### 5.4 结果与用户问题相关性评估

目前主要依赖：
1. **搜索API的相关性算法**
2. **LLM的智能查询词提取**
3. **用户问题的语义理解**

## 6. 架构设计

### 6.1 模块间调用关系

```mermaid
sequenceDiagram
    participant U as 用户
    participant API as API层
    participant QA as GeneralQAAgent  
    participant LLM as LLMService
    participant WS as WebSearchService
    participant WSC as WebSearchClient
    participant SA as 搜索API

    U->>API: 发送问题
    API->>QA: 创建对话任务
    QA->>LLM: 智能判断是否需要搜索
    LLM-->>QA: 返回判断结果
    
    alt 需要搜索
        QA->>WS: 执行搜索
        WS->>WSC: 发起HTTP请求
        WSC->>SA: 调用搜索API
        SA-->>WSC: 返回搜索结果
        WSC-->>WS: 解析结果
        WS-->>QA: 格式化结果
        QA->>QA: 整合搜索结果到对话
    end
    
    QA->>LLM: 生成最终回答
    LLM-->>QA: 返回回答
    QA-->>API: 返回结果
    API-->>U: 响应用户
```

### 6.2 数据流向

```
用户问题 → 智能判断 → 搜索执行 → 结果解析 → 格式化 → 上下文整合 → LLM生成 → 用户回答
```

### 6.3 缓存机制

**当前状态**: 暂未实现专门的缓存机制

**潜在改进点**:
- HTTP会话复用（已实现）
- 搜索结果缓存
- LLM判断结果缓存

### 6.4 并发查询处理

#### 异步处理

- 全面采用async/await异步编程
- aiohttp异步HTTP客户端
- 支持并发搜索多个provider

#### 并发控制

```python
# 配置中的并发限制
max_concurrent_requests: int = Field(default=10, env="MAX_CONCURRENT_REQUESTS")
```

## 7. 配置管理

### 7.1 核心配置项

```python
# 搜索API配置
web_search_api_url: str = Field(
    default="http://*************/fst/api/v1/websearch", 
    env="WEB_SEARCH_API_URL"
)

# 超时配置
web_scraping_timeout: int = Field(default=30, env="WEB_SCRAPING_TIMEOUT")
max_concurrent_requests: int = Field(default=10, env="MAX_CONCURRENT_REQUESTS")

# LLM配置
custom_llm_api_url: str = Field(
    default="http://*************/fst/api/v1/chat/completions",
    env="CUSTOM_LLM_API_URL"
)
default_llm_model: str = Field(default="ht::saas-deepseek-v3", env="DEFAULT_LLM_MODEL")
```

### 7.2 环境变量支持

所有配置项都支持通过环境变量覆盖，便于不同环境部署。

## 8. 流程图

### 8.1 完整搜索流程

```mermaid
flowchart TD
    A[用户发起对话] --> B{启用联网搜索?}
    B -->|否| C[直接LLM处理]
    B -->|是| D[LLM智能判断]
    D --> E{需要搜索?}
    E -->|否| C
    E -->|是| F[提取搜索关键词]
    F --> G[选择搜索源]
    G --> H[构建搜索请求]
    H --> I[发送HTTP请求]
    I --> J{请求成功?}
    J -->|否| K[记录错误日志]
    K --> C
    J -->|是| L[解析JSON响应]
    L --> M{解析成功?}
    M -->|否| K
    M -->|是| N[格式化搜索结果]
    N --> O[整合到对话上下文]
    O --> P[LLM生成最终回答]
    P --> Q[返回结果给用户]
    C --> P
```

### 8.2 错误处理流程

```mermaid
flowchart TD
    A[搜索请求] --> B{网络连接}
    B -->|失败| C[连接错误]
    B -->|成功| D{HTTP状态}
    D -->|4xx/5xx| E[HTTP错误]
    D -->|200| F{JSON解析}
    F -->|失败| G[解析错误]
    F -->|成功| H{业务逻辑}
    H -->|失败| I[业务错误]
    H -->|成功| J[返回结果]
    
    C --> K[记录错误日志]
    E --> K
    G --> K
    I --> K
    K --> L[优雅降级]
    L --> M[继续对话流程]
```

## 9. 当前方案优缺点分析

### 9.1 优点

1. **智能化程度高**: LLM智能判断搜索需求，避免不必要的搜索
2. **多源整合**: 支持内部wiki和外网搜索的统一接口
3. **异步高性能**: 全异步架构，支持高并发
4. **错误处理完善**: 多层错误处理，搜索失败不影响对话
5. **配置灵活**: 支持环境变量配置，便于部署
6. **工具调用支持**: 兼容OpenAI Function Calling标准

### 9.2 缺点

1. **缺乏缓存机制**: 重复搜索造成资源浪费
2. **无重试机制**: 网络临时故障时无自动重试
3. **结果相关性评估简单**: 主要依赖搜索API，缺乏二次筛选
4. **并发控制粗糙**: 缺乏细粒度的并发控制和限流
5. **监控指标不足**: 缺乏搜索成功率、响应时间等监控

### 9.3 改进建议

1. **增加缓存层**: 
   - Redis缓存搜索结果
   - LLM判断结果缓存
   - 设置合理的TTL策略

2. **完善重试机制**:
   - 指数退避重试
   - 不同错误类型的重试策略
   - 熔断器模式

3. **优化结果处理**:
   - 基于embedding的相关性重排
   - 结果去重和合并
   - 动态调整结果数量

4. **增强监控**:
   - 搜索API调用监控
   - 成功率和延迟指标
   - 异常告警机制

5. **性能优化**:
   - 连接池优化
   - 并发搜索多个源
   - 结果流式返回

## 10. 关键代码示例

### 10.1 智能搜索判断实现

<augment_code_snippet path="app/agents/qa/general_qa_agent.py" mode="EXCERPT">
```python
async def _analyze_search_need_with_llm(self, messages: list) -> tuple[bool, str, list]:
    """使用LLM分析消息历史，判断是否需要搜索以及搜索策略"""

    judge_system_prompt = """你是一个搜索需求分析助手。分析用户的对话是否需要联网搜索。

    分析规则：
    1. 天气、股价、新闻等实时信息 → 需要搜索
    2. 包含"今天"、"最新"、"现在"等时间词 → 需要搜索
    3. 公司内部术语、产品名称 → 需要搜索wiki
    4. 基础常识、创意任务 → 不需要搜索
    """
```
</augment_code_snippet>

### 10.2 搜索结果解析实现

<augment_code_snippet path="app/services/web_search_service.py" mode="EXCERPT">
```python
def _parse_search_results(self, response: Dict[str, Any], provider: Union[str, List[str]]) -> List[SearchResult]:
    """解析搜索API响应为标准格式"""
    results = []

    try:
        if "code" in response:
            if response["code"] == "0":
                data = response.get("data", {})

                if isinstance(data, dict) and "results" in data:
                    search_results = data["results"]

                    if isinstance(search_results, list):
                        for i, item in enumerate(search_results):
                            result = SearchResult(
                                title=item.get("title", ""),
                                content=item.get("content", ""),
                                url=item.get("url", ""),
                                source=item.get("providerName", item.get("provider", str(provider)))
                            )
                            results.append(result)
    except Exception as e:
        logger.error(f"解析搜索结果失败: {str(e)}", exc_info=True)

    return results
```
</augment_code_snippet>

### 10.3 工具调用执行实现

<augment_code_snippet path="app/tools/search_tools.py" mode="EXCERPT">
```python
async def execute_search(self, query: str, provider: List[str] = None,
                        max_results: int = 10, search_options: Dict[str, Any] = None) -> Dict[str, Any]:
    """执行搜索工具"""
    try:
        # 设置默认值
        if provider is None:
            provider = ["hiagent"]

        # 构建搜索参数
        search_params = {
            "query": query,
            "provider": provider,
            "snippet": False  # 获取完整内容
        }

        # 如果有原始搜索配置，提取认证信息
        if search_options and "authInfos" in search_options:
            search_params["authInfos"] = search_options["authInfos"]

        # 执行搜索
        results = await self.search_service.search(**search_params)

        # 格式化结果为工具返回格式
        if results:
            formatted_results = []
            for result in results[:max_results]:
                cleaned_title = self._clean_text(result.title)
                cleaned_content = self._clean_text(result.content)

                formatted_results.append({
                    "title": cleaned_title,
                    "content": cleaned_content,
                    "url": result.url,
                    "source": result.source
                })

            return {
                "success": True,
                "results_count": len(formatted_results),
                "results": formatted_results,
                "search_summary": f"找到 {len(formatted_results)} 条关于 '{query}' 的搜索结果"
            }
    except Exception as e:
        return {
            "success": False,
            "results_count": 0,
            "results": [],
            "search_summary": f"搜索 '{query}' 时出现错误",
            "error": str(e)
        }
```
</augment_code_snippet>

## 11. 性能指标和监控

### 11.1 关键性能指标

| 指标类型 | 指标名称 | 当前状态 | 建议目标 |
|---------|---------|---------|---------|
| 响应时间 | 搜索API调用延迟 | 未监控 | < 2秒 |
| 成功率 | 搜索成功率 | 未监控 | > 95% |
| 并发性 | 最大并发搜索数 | 10 | 可配置 |
| 超时设置 | HTTP请求超时 | 180秒 | 合理 |
| 错误处理 | 错误日志覆盖率 | 完善 | 保持 |

### 11.2 监控建议

1. **API调用监控**
   - 请求量统计
   - 响应时间分布
   - 错误率趋势

2. **业务指标监控**
   - 搜索触发率
   - 不同provider使用比例
   - 用户满意度反馈

3. **系统资源监控**
   - 内存使用情况
   - 网络连接数
   - CPU使用率

## 12. 部署和运维

### 12.1 环境配置

```bash
# 搜索服务配置
export WEB_SEARCH_API_URL="http://*************/fst/api/v1/websearch"
export WEB_SCRAPING_TIMEOUT=30
export MAX_CONCURRENT_REQUESTS=10

# LLM服务配置
export CUSTOM_LLM_API_URL="http://*************/fst/api/v1/chat/completions"
export DEFAULT_LLM_MODEL="ht::saas-deepseek-v3"

# 日志配置
export LOG_LEVEL="INFO"
export LOG_FORMAT="json"
```

### 12.2 健康检查

系统提供健康检查接口，可用于监控服务状态：

```python
@router.get("/health")
async def health_check():
    return {
        "status": "healthy",
        "timestamp": datetime.utcnow().isoformat(),
        "services": {
            "web_search": "available",
            "llm_service": "available"
        }
    }
```

### 12.3 故障排查

常见问题和解决方案：

1. **搜索API超时**
   - 检查网络连接
   - 调整超时设置
   - 查看API服务状态

2. **解析错误**
   - 检查API响应格式变化
   - 验证JSON结构
   - 查看详细错误日志

3. **LLM判断异常**
   - 检查LLM服务可用性
   - 验证prompt格式
   - 查看模型响应

## 13. 总结

该联网查询实现方案整体架构合理，采用了现代化的异步编程模式和智能化的搜索判断机制。在功能完整性和错误处理方面表现良好，但在性能优化和监控方面还有改进空间。

**核心优势：**
- 智能化搜索判断，减少不必要的API调用
- 多搜索源统一接口，支持内外网信息整合
- 完善的错误处理机制，保证系统稳定性
- 异步架构设计，支持高并发场景

**改进方向：**
- 引入缓存机制提升性能
- 完善监控和告警体系
- 优化结果相关性评估
- 增强并发控制和限流

建议重点关注缓存机制的引入和监控体系的完善，以进一步提升系统的性能和可维护性。
