# 应用配置
APP_NAME=Web Assistant Agents
APP_VERSION=1.0.0
DEBUG=true
LOG_LEVEL=INFO

# 服务配置
HOST=0.0.0.0
PORT=8000
WORKERS=4

# 自定义LLM API配置
CUSTOM_LLM_API_URL=http://*************/web/unauth/LLM_api_proxy/v1/chat/completions
DEFAULT_LLM_MODEL=ht::saas-deepseek-v3

# 可用模型列表（逗号分隔）
AVAILABLE_MODELS=ht::saas-deepseek-v3,ht::saas-deepseek-r1,ht::saas-doubao-15-pro-32k,ht::saas-deepseek-r1-thinking,ht::qwen-25-14b-int4,ht::qwen-25-14b-int4-noft,ht::local-qwq-32b

# 认证配置（可选）
SECRET_KEY=your_super_secret_key_here_change_in_production
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# 限流配置
RATE_LIMIT_REQUESTS=100
RATE_LIMIT_WINDOW=60

# 监控配置
ENABLE_METRICS=true
METRICS_PORT=9090

# 日志配置
LOG_FORMAT=json
LOG_FILE=logs/app.log

# 文件上传配置
MAX_FILE_SIZE=10485760  # 10MB
ALLOWED_FILE_TYPES=jpg,jpeg,png,gif,pdf,docx,txt

# 外部服务配置
WEB_SCRAPING_TIMEOUT=30
MAX_CONCURRENT_REQUESTS=10

# 开发配置
RELOAD=true
ACCESS_LOG=true
