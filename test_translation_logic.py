#!/usr/bin/env python3
"""
测试翻译逻辑的单元测试
"""
import asyncio
import json
from typing import AsyncGenerator


class MockLLMService:
    """模拟LLM服务"""
    
    async def stream_chat_completion(self, messages, model=None, max_tokens=None, temperature=None):
        """模拟流式聊天补全"""
        # 模拟流式响应数据
        mock_responses = [
            '{"choices": [{"delta": {"content": "你好", "role": "assistant"}, "index": 0}]}',
            '{"choices": [{"delta": {"content": "，", "role": null}, "index": 0}]}',
            '{"choices": [{"delta": {"content": "今天", "role": null}, "index": 0}]}',
            '{"choices": [{"delta": {"content": "过得", "role": null}, "index": 0}]}',
            '{"choices": [{"delta": {"content": "怎么样", "role": null}, "index": 0}]}',
            '{"choices": [{"delta": {"content": "？", "role": null}, "index": 0}]}',
            '[DONE]'
        ]
        
        for response in mock_responses:
            yield response
            await asyncio.sleep(0.1)  # 模拟网络延迟


class MockTranslationService:
    """模拟翻译服务"""
    
    def __init__(self):
        self.llm_service = MockLLMService()
    
    async def stream_translate_single_text(self, text: str) -> AsyncGenerator[str, None]:
        """模拟单个文本的流式翻译"""
        print(f"开始流式翻译: {text}")
        
        # 初始化结果数据
        result_data = [{"text": text, "translateText": ""}]
        
        # 获取流式响应
        stream_generator = self.llm_service.stream_chat_completion(
            messages=[{"role": "user", "content": text}]
        )
        
        accumulated_content = ""
        async for chunk in stream_generator:
            try:
                chunk_data = chunk.strip()
                
                if not chunk_data or chunk_data == "[DONE]":
                    if chunk_data == "[DONE]":
                        break
                    continue
                
                # 解析JSON数据
                chunk_json = json.loads(chunk_data)
                
                # 提取增量内容
                if "choices" in chunk_json and chunk_json["choices"]:
                    choice = chunk_json["choices"][0]
                    if "delta" in choice and "content" in choice["delta"]:
                        delta_content = choice["delta"]["content"]
                        if delta_content:
                            accumulated_content += delta_content
                            
                            # 更新翻译结果
                            result_data[0]["translateText"] = accumulated_content
                            
                            # 构建流式响应数据
                            response_data = {
                                "code": "success",
                                "message": "翻译中",
                                "data": result_data.copy()
                            }
                            
                            yield f"data: {json.dumps(response_data, ensure_ascii=False)}\n\n"
            except (json.JSONDecodeError, KeyError) as e:
                print(f"解析流式数据失败: {str(e)}, chunk: {chunk}")
                continue
        
        # 发送最终完成响应
        final_response = {
            "code": "success",
            "message": "翻译完成",
            "data": result_data
        }
        yield f"data: {json.dumps(final_response, ensure_ascii=False)}\n\n"
        yield "data: [DONE]\n\n"


async def test_stream_translation_logic():
    """测试流式翻译逻辑"""
    print("=== 测试流式翻译逻辑 ===")
    
    service = MockTranslationService()
    
    # 测试文本
    test_text = "Hello, how are you today?"
    print(f"测试文本: {test_text}")
    
    print("\n--- 开始流式翻译 ---")
    chunk_count = 0
    async for chunk in service.stream_translate_single_text(test_text):
        chunk_count += 1
        print(f"流式数据 {chunk_count}: {chunk.strip()}")
        
        # 解析并显示翻译进度
        if chunk.startswith("data: ") and not chunk.strip().endswith("[DONE]"):
            try:
                data_str = chunk[6:].strip()
                if data_str:
                    data = json.loads(data_str)
                    if "data" in data and data["data"]:
                        translate_text = data["data"][0].get("translateText", "")
                        print(f"  -> 当前翻译: '{translate_text}'")
            except:
                pass
    
    print(f"\n总共接收到 {chunk_count} 个流式数据块")
    print("=== 测试完成 ===")


if __name__ == "__main__":
    asyncio.run(test_stream_translation_logic())
