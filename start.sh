#!/bin/bash
###
 # @Author: xuzhouchen
 # @Date: 2025-07-23 09:38:26
 # @LastEditors: 021178
 # @LastEditTime: 2025-07-28 13:09:50
 # @Description: 本地开发启动脚本 (使用 uv)
###

# Web Assistant Agents 本地启动脚本

set -e

# 颜色定义
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m'

# 默认配置
DEFAULT_HOST="localhost"
DEFAULT_PORT="8000"

# 解析命令行参数
HOST="$DEFAULT_HOST"
PORT="$DEFAULT_PORT"
RELOAD="--reload"

while [[ $# -gt 0 ]]; do
    case $1 in
        --host)
            HOST="$2"
            shift 2
            ;;
        --port)
            PORT="$2"
            shift 2
            ;;
        --prod|--production)
            RELOAD=""
            shift
            ;;
        --help|-h)
            echo "用法: $0 [选项]"
            echo ""
            echo "选项:"
            echo "  --host HOST        指定主机地址 (默认: $DEFAULT_HOST)"
            echo "  --port PORT        指定端口号 (默认: $DEFAULT_PORT)"
            echo "  --prod             生产模式 (禁用热重载)"
            echo "  --help, -h         显示此帮助信息"
            echo ""
            echo "示例:"
            echo "  $0                 # 启动开发服务器"
            echo "  $0 --port 8080     # 在端口 8080 启动"
            echo "  $0 --prod          # 生产模式启动"
            echo ""
            echo "注意: 此脚本用于本地开发，使用 uv 管理依赖"
            echo "      部署请使用 Docker: docker-compose up"
            exit 0
            ;;
        *)
            echo -e "${RED}❌ 未知选项: $1${NC}"
            echo "使用 --help 查看帮助"
            exit 1
            ;;
    esac
done

echo -e "${BLUE}🚀 启动 Web Assistant Agents (本地开发模式)${NC}"

# 检查 uv 是否安装
if ! command -v uv &> /dev/null; then
    echo -e "${RED}❌ uv 未安装，请先安装：${NC}"
    echo "curl -LsSf https://astral.sh/uv/install.sh | sh"
    exit 1
fi

# 同步依赖
echo -e "${BLUE}📦 同步项目依赖...${NC}"
uv sync

# 显示启动信息
if [ -n "$RELOAD" ]; then
    echo -e "${GREEN}✅ 启动开发服务器 (热重载已启用)${NC}"
else
    echo -e "${YELLOW}✅ 启动生产服务器 (热重载已禁用)${NC}"
fi

echo -e "${GREEN}🌐 服务地址: http://$HOST:$PORT${NC}"
echo -e "${GREEN}📚 API 文档: http://$HOST:$PORT/docs${NC}"
echo ""

# 启动服务器
uv run uvicorn app.main:app $RELOAD --host "$HOST" --port "$PORT" \
    --timeout-keep-alive 300 \
    --timeout-graceful-shutdown 300 \
    --limit-concurrency 1000 \
    --limit-max-requests 10000
