#!/usr/bin/env python3
"""
流式翻译功能使用示例
演示如何使用新的流式翻译API
"""
import asyncio
import aiohttp
import json
from typing import List, Dict


class StreamTranslationClient:
    """流式翻译客户端示例"""
    
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url
        self.session = None
    
    async def get_session(self):
        if self.session is None or self.session.closed:
            self.session = aiohttp.ClientSession()
        return self.session
    
    async def translate_stream(self, 
                             question: List[Dict[str, str]], 
                             provider: str = "llm_translate",
                             src_lang: str = "en", 
                             tgt_lang: str = "zh"):
        """流式翻译"""
        session = await self.get_session()
        
        payload = {
            "question": question,
            "stream": True,
            "provider": provider,
            "translateOptions": {
                "src_lang": src_lang,
                "tgt_lang": tgt_lang
            }
        }
        
        print(f"发送流式翻译请求: {json.dumps(payload, ensure_ascii=False, indent=2)}")
        
        async with session.post(
            f"{self.base_url}/api/v1/translation/translate",
            json=payload,
            headers={"Content-Type": "application/json"}
        ) as response:
            print(f"响应状态: {response.status}")
            print(f"响应头: {dict(response.headers)}")
            
            if response.status == 200:
                print("\n=== 开始接收流式数据 ===")
                chunk_count = 0
                
                async for line in response.content:
                    line_str = line.decode('utf-8').strip()
                    if line_str:
                        chunk_count += 1
                        print(f"\n[流式数据 {chunk_count}]")
                        print(f"原始数据: {line_str}")
                        
                        # 解析流式数据
                        if line_str.startswith("data: "):
                            data_str = line_str[6:]
                            if data_str == "[DONE]":
                                print("流式传输完成")
                                break
                            
                            try:
                                data = json.loads(data_str)
                                print(f"解析结果: {json.dumps(data, ensure_ascii=False, indent=2)}")
                                
                                # 显示翻译进度
                                if "data" in data and data["data"]:
                                    for i, item in enumerate(data["data"]):
                                        original = item.get("text", "")
                                        translated = item.get("translateText", "")
                                        print(f"文本 {i+1}: '{original}' -> '{translated}'")
                                        
                            except json.JSONDecodeError as e:
                                print(f"JSON解析失败: {e}")
                
                print(f"\n总共接收到 {chunk_count} 个数据块")
            else:
                error_text = await response.text()
                print(f"请求失败: {error_text}")
    
    async def translate_non_stream(self, 
                                 question: List[Dict[str, str]], 
                                 provider: str = "doubao",
                                 src_lang: str = "en", 
                                 tgt_lang: str = "zh"):
        """非流式翻译"""
        session = await self.get_session()
        
        payload = {
            "question": question,
            "stream": False,
            "provider": provider,
            "translateOptions": {
                "src_lang": src_lang,
                "tgt_lang": tgt_lang
            }
        }
        
        print(f"发送非流式翻译请求: {json.dumps(payload, ensure_ascii=False, indent=2)}")
        
        async with session.post(
            f"{self.base_url}/api/v1/translation/translate",
            json=payload,
            headers={"Content-Type": "application/json"}
        ) as response:
            print(f"响应状态: {response.status}")
            
            if response.status == 200:
                result = await response.json()
                print(f"翻译结果: {json.dumps(result, ensure_ascii=False, indent=2)}")
                return result
            else:
                error_text = await response.text()
                print(f"请求失败: {error_text}")
                return None
    
    async def close(self):
        if self.session and not self.session.closed:
            await self.session.close()


async def demo_stream_translation():
    """演示流式翻译功能"""
    client = StreamTranslationClient()
    
    # 测试数据
    test_questions = [
        {"text": "Hello, how are you today?"},
        {"text": "This is a test message for stream translation."},
        {"text": "I hope this feature works well!"}
    ]
    
    try:
        print("=" * 60)
        print("流式翻译功能演示")
        print("=" * 60)
        
        # 测试1: Doubao + 流式（应该返回非流式结果的流式包装）
        print("\n【测试1】Doubao 提供商 + 流式模式")
        print("-" * 40)
        await client.translate_stream(
            question=test_questions,
            provider="doubao",
            src_lang="en",
            tgt_lang="zh"
        )
        
        # 测试2: LLM + 非流式
        print("\n【测试2】LLM 提供商 + 非流式模式")
        print("-" * 40)
        await client.translate_non_stream(
            question=test_questions[:1],  # 只测试一个文本
            provider="llm_translate",
            src_lang="en",
            tgt_lang="zh"
        )
        
        # 测试3: LLM + 流式（真正的流式翻译）
        print("\n【测试3】LLM 提供商 + 流式模式（真正的流式翻译）")
        print("-" * 40)
        await client.translate_stream(
            question=test_questions,
            provider="llm_translate",
            src_lang="en",
            tgt_lang="zh"
        )
        
    except Exception as e:
        print(f"演示过程中出现错误: {str(e)}")
    finally:
        await client.close()
    
    print("\n" + "=" * 60)
    print("演示完成")
    print("=" * 60)


if __name__ == "__main__":
    print("流式翻译功能演示")
    print("注意: 需要先启动服务器 (uvicorn app.main:app --reload)")
    print("如果服务器未启动，将显示连接错误")
    print()
    
    asyncio.run(demo_stream_translation())
