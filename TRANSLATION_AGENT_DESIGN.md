# 翻译Agent设计文档

## 1. 架构设计概述

### 1.1 整体架构图

```mermaid
graph TB
    A[API Layer] --> B[Translation Agent]
    B --> C[LangGraph Workflow]
    C --> D[Translation Service]
    D --> E[Doubao Client]
    D --> F[LLM Client]
    F --> G[LLM Service]
    E --> H[Doubao API]
    G --> I[LLM Provider APIs]
    
    subgraph "Translation Agent"
        C1[validate_input]
        C2[detect_language]
        C3[select_provider]
        C4[translate_text]
        C5[fallback_translate]
        C6[finalize]
        
        C1 --> C2
        C2 --> C3
        C3 --> C4
        C4 --> C5
        C4 --> C6
        C5 --> C6
    end
```

### 1.2 组件关系

- **API Layer**: FastAPI路由层，处理HTTP请求和响应
- **Translation Agent**: 基于LangGraph的翻译智能体，协调整个翻译流程
- **Translation Service**: 翻译服务管理器，负责提供商选择和调用
- **Provider Clients**: 具体的翻译提供商客户端实现
- **LLM Service**: 大语言模型服务，支持多种LLM提供商

### 1.3 数据流向

1. **请求流**: API → Agent → Workflow → Service → Provider
2. **响应流**: Provider → Service → Workflow → Agent → API
3. **流式数据**: Provider → Service → Agent → API (实时流式传输)

## 2. 提供商支持策略

### 2.1 Doubao提供商集成

#### 2.1.1 API调用流程
```python
# Doubao API调用示例
payload = {
    "data": {
        "text": ["Hello", "World"],
        "src_lang": "en",
        "tgt_lang": "zh",
        "model_name": "doubao"
    }
}

# HTTP POST请求到Doubao API
response = await session.post(doubao_api_url, json=payload)
```

#### 2.1.2 特性支持
- ✅ 批量文本翻译
- ✅ 多语言对支持
- ❌ 流式翻译（不支持）
- ✅ 高并发处理
- ✅ 专业术语翻译

### 2.2 LLM提供商集成

#### 2.2.1 模型选择策略
```python
# 支持的LLM模型
SUPPORTED_MODELS = [
    "gpt-4o",
    "gpt-4o-mini", 
    "claude-3-5-sonnet-20241022",
    "claude-3-5-haiku-20241022"
]

# 动态模型选择
model = request.model or settings.default_llm_model
```

#### 2.2.2 特性支持
- ✅ 流式翻译
- ✅ 多模型支持
- ✅ 上下文理解
- ✅ 专业领域翻译
- ✅ 自定义提示词

### 2.3 提供商切换和回退机制

#### 2.3.1 智能回退逻辑
```python
def get_fallback_provider(current_provider):
    """获取回退提供商"""
    if current_provider == TranslationProvider.DOUBAO:
        return TranslationProvider.LLM_TRANSLATE
    else:
        return TranslationProvider.DOUBAO
```

#### 2.3.2 回退触发条件
- API调用失败或超时
- 返回结果质量不符合预期
- 提供商服务不可用
- 达到最大重试次数

### 2.4 扩展新提供商的标准化接口

#### 2.4.1 提供商接口规范
```python
class TranslationProviderInterface:
    """翻译提供商标准接口"""
    
    async def translate(self, request: TranslationRequest) -> TranslationResult:
        """非流式翻译"""
        raise NotImplementedError
    
    async def stream_translate(self, request: TranslationRequest) -> AsyncGenerator[str, None]:
        """流式翻译（可选实现）"""
        raise NotImplementedError
    
    def supports_streaming(self) -> bool:
        """是否支持流式翻译"""
        return False
    
    def get_supported_languages(self) -> List[str]:
        """获取支持的语言列表"""
        raise NotImplementedError
```

#### 2.4.2 新提供商集成步骤
1. 实现 `TranslationProviderInterface` 接口
2. 在 `TranslationProvider` 枚举中添加新提供商
3. 在 `TranslationService` 中添加路由逻辑
4. 配置相关环境变量和设置
5. 编写单元测试和集成测试

## 3. 流式与非流式处理机制

### 3.1 三分支处理逻辑

#### 3.1.1 分支判断逻辑
```python
def determine_processing_branch(provider: str, stream: bool) -> str:
    """确定处理分支"""
    if provider == TranslationProvider.DOUBAO:
        return "doubao_non_stream"  # 分支1
    elif provider == TranslationProvider.LLM_TRANSLATE and not stream:
        return "llm_non_stream"     # 分支2  
    elif provider == TranslationProvider.LLM_TRANSLATE and stream:
        return "llm_stream"         # 分支3
    else:
        return "default_non_stream"
```

#### 3.1.2 分支处理详情

**分支1: Doubao非流式**
- 批量发送所有文本到Doubao API
- 等待完整响应后返回结果
- 适用于对实时性要求不高的场景

**分支2: LLM非流式**  
- 逐个文本调用LLM API
- 等待每个文本翻译完成后返回
- 支持复杂的上下文理解

**分支3: LLM流式**
- 并发启动多个文本的流式翻译
- 实时返回增量翻译结果
- 提供最佳的用户体验

### 3.2 流式数据解析和累积机制

#### 3.2.1 SSE数据解析
```python
def parse_sse_chunk(chunk: str) -> dict:
    """解析SSE数据块"""
    # 移除data:前缀
    if chunk.startswith("data: "):
        chunk = chunk[6:]
    
    # 跳过空行和结束标记
    if not chunk or chunk == "[DONE]":
        return None
    
    # 解析JSON
    return json.loads(chunk)
```

#### 3.2.2 增量内容累积
```python
# 标准化增量格式
delta_response = {
    "delta": {
        "index": 0,           # 文本在数组中的索引
        "text": "Hello",      # 原始文本
        "translateText": "你好", # 累积的翻译内容
        "finished": False     # 是否完成翻译
    }
}
```

### 3.3 SSE格式数据传输协议

#### 3.3.1 协议规范
```
data: {"delta":{"index":0,"text":"Hello","translateText":"你"}}

data: {"delta":{"index":0,"text":"Hello","translateText":"你好"}}

data: {"delta":{"index":0,"text":"Hello","translateText":"你好","finished":true}}

data: [DONE]
```

#### 3.3.2 客户端处理示例
```javascript
const eventSource = new EventSource('/api/v1/translation/translate');
const translations = {};

eventSource.onmessage = function(event) {
    if (event.data === '[DONE]') {
        eventSource.close();
        return;
    }
    
    const data = JSON.parse(event.data);
    if (data.delta) {
        const {index, text, translateText, finished} = data.delta;
        translations[index] = {text, translateText, finished};
        updateUI(translations);
    }
};
```

### 3.4 流式响应错误处理和中断恢复

#### 3.4.1 错误处理策略
- **连接中断**: 自动重连机制，保持翻译状态
- **解析错误**: 跳过错误数据块，继续处理后续数据
- **超时处理**: 设置合理的超时时间，避免无限等待
- **部分失败**: 标记失败的文本，继续处理其他文本

#### 3.4.2 中断恢复机制
```python
class StreamRecoveryManager:
    """流式恢复管理器"""
    
    def __init__(self):
        self.translation_states = {}
        self.failed_indices = set()
    
    async def recover_failed_translations(self):
        """恢复失败的翻译"""
        for index in self.failed_indices:
            # 重新启动失败的翻译任务
            await self.restart_translation(index)
```

## 4. API接口规范

### 4.1 请求参数定义

#### 4.1.1 翻译请求结构
```python
class TranslationRequest(BaseModel):
    """翻译请求模型"""
    question: List[Dict[str, str]]  # [{"text": "Hello"}, {"text": "World"}]
    stream: bool = False            # 是否启用流式翻译
    provider: str = "doubao"        # 翻译提供商
    translateOptions: Dict[str, str] = {
        "src_lang": "en",           # 源语言
        "tgt_lang": "zh"            # 目标语言
    }
```

#### 4.1.2 参数验证规则
```python
def validate_translation_request(request: TranslationRequest):
    """验证翻译请求参数"""
    # 验证question字段
    if not request.question or not isinstance(request.question, list):
        raise ValueError("question must be a non-empty list")
    
    # 验证每个question项
    for item in request.question:
        if not isinstance(item, dict) or "text" not in item:
            raise ValueError("Each question item must have 'text' field")
    
    # 验证提供商
    if request.provider not in ["doubao", "llm_translate"]:
        raise ValueError("Invalid provider")
    
    # 验证语言代码
    supported_langs = ["en", "zh", "ja", "ko", "fr", "de", "es", "ru"]
    src_lang = request.translateOptions.get("src_lang", "en")
    tgt_lang = request.translateOptions.get("tgt_lang", "zh")
    
    if src_lang not in supported_langs or tgt_lang not in supported_langs:
        raise ValueError("Unsupported language")
```

### 4.2 响应格式标准化定义

#### 4.2.1 成功响应格式
```python
# 非流式响应
{
    "code": "success",
    "message": "翻译成功", 
    "data": [
        {
            "text": "Hello",
            "translateText": "你好"
        },
        {
            "text": "World", 
            "translateText": "世界"
        }
    ]
}

# 流式响应 (SSE格式)
data: {"delta":{"index":0,"text":"Hello","translateText":"你好"}}
data: {"delta":{"index":1,"text":"World","translateText":"世界"}}
data: [DONE]
```

#### 4.2.2 错误响应格式
```python
# 非流式错误响应
{
    "code": "error",
    "message": "翻译失败: 具体错误信息",
    "data": []
}

# 流式错误响应
data: {"delta":{"error":true,"message":"翻译失败: 具体错误信息"}}
data: [DONE]
```

### 4.3 兼容性保证和版本管理

#### 4.3.1 API版本策略
- 当前版本: `v1`
- 版本路径: `/api/v1/translation/translate`
- 向后兼容: 保证v1版本的稳定性
- 新功能: 通过可选参数扩展，不破坏现有接口

#### 4.3.2 兼容性检查清单
- ✅ 请求参数向后兼容
- ✅ 响应格式保持一致
- ✅ 错误码和消息标准化
- ✅ 流式和非流式模式共存
- ✅ 提供商切换透明化

## 5. 性能和可靠性设计

### 5.1 并发处理能力

#### 5.1.1 并发架构设计
```python
# 并发翻译实现
async def concurrent_translate(texts: List[str]) -> AsyncGenerator:
    """并发翻译多个文本"""
    # 创建并发任务
    tasks = [
        asyncio.create_task(translate_single_text(text, index))
        for index, text in enumerate(texts)
    ]
    
    # 使用队列合并流式输出
    async for result in merge_concurrent_streams(tasks):
        yield result
```

#### 5.1.2 资源管理策略
- **连接池**: 复用HTTP连接，减少连接开销
- **任务队列**: 限制并发任务数量，防止资源耗尽
- **内存管理**: 及时释放已完成翻译的中间数据
- **超时控制**: 设置合理的超时时间，避免资源泄露

### 5.2 错误处理和降级策略

#### 5.2.1 多层错误处理
```python
# 错误处理层次
1. 网络层错误 -> 重试机制
2. API调用错误 -> 提供商切换  
3. 解析错误 -> 跳过错误数据
4. 业务逻辑错误 -> 降级处理
```

#### 5.2.2 降级策略
- **提供商降级**: Doubao失败 → LLM提供商
- **功能降级**: 流式失败 → 非流式模式
- **质量降级**: 复杂翻译失败 → 简单翻译
- **服务降级**: 翻译服务不可用 → 返回原文

### 5.3 监控指标和日志记录

#### 5.3.1 关键监控指标
```python
# 性能指标
- 翻译请求QPS
- 平均响应时间
- 流式数据传输速率
- 并发任务数量

# 质量指标  
- 翻译成功率
- 提供商可用性
- 错误率分布
- 用户满意度

# 资源指标
- CPU使用率
- 内存使用量
- 网络带宽
- 连接池状态
```

#### 5.3.2 日志记录规范
```python
# 日志级别和内容
INFO: 正常业务流程记录
WARN: 异常情况但不影响服务
ERROR: 错误情况需要关注
DEBUG: 详细调试信息

# 日志格式示例
[2024-01-01 12:00:00] INFO [TranslationAgent] provider-llm-translate, stream=true, texts=3
[2024-01-01 12:00:01] DEBUG [LLMClient] 开始流式翻译文本 0: Hello world...
[2024-01-01 12:00:02] WARN [StreamParser] 解析流式数据失败: Invalid JSON
[2024-01-01 12:00:03] INFO [TranslationAgent] 翻译完成: 3/3 成功
```

### 5.4 缓存机制和性能优化

#### 5.4.1 多级缓存策略
```python
# 缓存层次
1. 内存缓存: 热点翻译结果 (Redis)
2. 本地缓存: 常用语言对 (LRU Cache)  
3. 数据库缓存: 历史翻译记录 (PostgreSQL)
```

#### 5.4.2 性能优化方案
- **批量处理**: 合并小文本减少API调用
- **预加载**: 预热常用翻译模型
- **压缩传输**: 启用gzip压缩减少带宽
- **CDN加速**: 静态资源CDN分发

## 6. 使用示例和最佳实践

### 6.1 不同场景调用示例

#### 6.1.1 简单文本翻译
```python
# 非流式翻译
response = await client.post("/api/v1/translation/translate", json={
    "question": [{"text": "Hello world"}],
    "stream": False,
    "provider": "doubao",
    "translateOptions": {
        "src_lang": "en",
        "tgt_lang": "zh"
    }
})

result = response.json()
print(result["data"][0]["translateText"])  # 输出: 你好世界
```

#### 6.1.2 批量文本翻译
```python
# 批量翻译多个文本
texts = [
    {"text": "Good morning"},
    {"text": "How are you?"},
    {"text": "Nice to meet you"}
]

response = await client.post("/api/v1/translation/translate", json={
    "question": texts,
    "stream": False,
    "provider": "doubao"
})
```

#### 6.1.3 流式翻译
```python
# 流式翻译获取实时进度
async with client.stream("POST", "/api/v1/translation/translate", json={
    "question": [{"text": "This is a long sentence for translation"}],
    "stream": True,
    "provider": "llm_translate"
}) as response:
    async for line in response.aiter_lines():
        if line.startswith("data: "):
            data = json.loads(line[6:])
            if "delta" in data:
                print(f"翻译进度: {data['delta']['translateText']}")
```

### 6.2 客户端集成最佳实践

#### 6.2.1 JavaScript客户端
```javascript
class TranslationClient {
    constructor(baseUrl) {
        this.baseUrl = baseUrl;
    }
    
    async translateStream(texts, options = {}) {
        const response = await fetch(`${this.baseUrl}/api/v1/translation/translate`, {
            method: 'POST',
            headers: {'Content-Type': 'application/json'},
            body: JSON.stringify({
                question: texts.map(text => ({text})),
                stream: true,
                provider: options.provider || 'llm_translate',
                translateOptions: {
                    src_lang: options.srcLang || 'en',
                    tgt_lang: options.tgtLang || 'zh'
                }
            })
        });
        
        const reader = response.body.getReader();
        const translations = {};
        
        while (true) {
            const {done, value} = await reader.read();
            if (done) break;
            
            const chunk = new TextDecoder().decode(value);
            const lines = chunk.split('\n');
            
            for (const line of lines) {
                if (line.startsWith('data: ')) {
                    const data = line.slice(6);
                    if (data === '[DONE]') return translations;
                    
                    try {
                        const result = JSON.parse(data);
                        if (result.delta) {
                            const {index, text, translateText, finished} = result.delta;
                            translations[index] = {text, translateText, finished};
                            
                            // 触发进度回调
                            if (options.onProgress) {
                                options.onProgress(index, translateText, finished);
                            }
                        }
                    } catch (e) {
                        console.warn('解析流式数据失败:', e);
                    }
                }
            }
        }
        
        return translations;
    }
}

// 使用示例
const client = new TranslationClient('http://localhost:8000');
const translations = await client.translateStream(
    ['Hello', 'World', 'How are you?'],
    {
        provider: 'llm_translate',
        srcLang: 'en',
        tgtLang: 'zh',
        onProgress: (index, text, finished) => {
            console.log(`文本 ${index}: ${text} ${finished ? '(完成)' : '(进行中)'}`);
        }
    }
);
```

#### 6.2.2 Python客户端
```python
import aiohttp
import asyncio
import json

class AsyncTranslationClient:
    def __init__(self, base_url: str):
        self.base_url = base_url
        self.session = None
    
    async def __aenter__(self):
        self.session = aiohttp.ClientSession()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()
    
    async def translate_stream(self, texts: List[str], **options):
        """流式翻译"""
        payload = {
            "question": [{"text": text} for text in texts],
            "stream": True,
            "provider": options.get("provider", "llm_translate"),
            "translateOptions": {
                "src_lang": options.get("src_lang", "en"),
                "tgt_lang": options.get("tgt_lang", "zh")
            }
        }
        
        async with self.session.post(
            f"{self.base_url}/api/v1/translation/translate",
            json=payload
        ) as response:
            translations = {}
            
            async for line in response.content:
                line_str = line.decode('utf-8').strip()
                if line_str.startswith('data: '):
                    data_str = line_str[6:]
                    if data_str == '[DONE]':
                        break
                    
                    try:
                        data = json.loads(data_str)
                        if 'delta' in data:
                            delta = data['delta']
                            index = delta['index']
                            translations[index] = {
                                'text': delta['text'],
                                'translateText': delta['translateText'],
                                'finished': delta.get('finished', False)
                            }
                            
                            # 调用进度回调
                            if 'on_progress' in options:
                                await options['on_progress'](index, delta)
                                
                    except json.JSONDecodeError:
                        continue
            
            return translations

# 使用示例
async def main():
    async with AsyncTranslationClient('http://localhost:8000') as client:
        async def progress_callback(index, delta):
            print(f"进度更新 - 文本 {index}: {delta['translateText']}")
        
        results = await client.translate_stream(
            ['Hello world', 'Good morning', 'How are you?'],
            provider='llm_translate',
            src_lang='en',
            tgt_lang='zh',
            on_progress=progress_callback
        )
        
        for index, result in results.items():
            print(f"最终结果 {index}: {result['text']} -> {result['translateText']}")

asyncio.run(main())
```

### 6.3 常见问题排查和解决方案

#### 6.3.1 常见问题清单

**Q1: 流式翻译中断或无响应**
```
原因: 网络连接不稳定或服务器超时
解决: 
1. 检查网络连接状态
2. 增加客户端超时时间
3. 实现重连机制
4. 检查服务器日志
```

**Q2: 翻译质量不符合预期**
```
原因: 提供商选择不当或提示词不合适
解决:
1. 尝试切换到LLM提供商
2. 调整翻译提示词
3. 检查源语言检测是否正确
4. 使用专业术语词典
```

**Q3: 并发翻译性能问题**
```
原因: 并发数过高或资源不足
解决:
1. 调整并发任务数量限制
2. 增加服务器资源配置
3. 启用缓存机制
4. 优化批量处理策略
```

**Q4: API调用频率限制**
```
原因: 超出提供商API调用限制
解决:
1. 实现请求频率控制
2. 使用多个API密钥轮换
3. 启用缓存减少重复调用
4. 切换到其他提供商
```

#### 6.3.2 调试和监控工具

**日志分析工具**
```bash
# 查看翻译请求日志
grep "TranslationAgent" /var/log/app.log | tail -100

# 分析错误率
grep "ERROR.*Translation" /var/log/app.log | wc -l

# 监控流式翻译性能
grep "stream.*complete" /var/log/app.log | awk '{print $3}' | sort | uniq -c
```

**性能监控脚本**
```python
# 翻译性能测试脚本
async def performance_test():
    """翻译性能测试"""
    import time
    
    texts = ["Hello world"] * 100  # 100个测试文本
    start_time = time.time()
    
    async with AsyncTranslationClient('http://localhost:8000') as client:
        results = await client.translate_stream(texts)
    
    end_time = time.time()
    duration = end_time - start_time
    
    print(f"翻译 {len(texts)} 个文本耗时: {duration:.2f}秒")
    print(f"平均每个文本: {duration/len(texts):.3f}秒")
    print(f"QPS: {len(texts)/duration:.2f}")
```

---

## 总结

本翻译Agent设计文档详细描述了基于LangGraph的多提供商翻译系统的完整架构和实现方案。系统具备以下核心特性：

1. **多提供商支持**: 灵活支持Doubao和LLM提供商，具备智能切换和回退能力
2. **流式翻译**: 支持实时流式翻译，提供优秀的用户体验
3. **并发处理**: 支持多文本并发翻译，显著提升处理效率
4. **标准化接口**: 提供统一的API接口，便于客户端集成
5. **高可靠性**: 完善的错误处理、监控和降级机制
6. **易扩展性**: 标准化的提供商接口，便于集成新的翻译服务

该设计文档可作为开发团队的标准参考，指导翻译Agent的开发、部署和维护工作。
