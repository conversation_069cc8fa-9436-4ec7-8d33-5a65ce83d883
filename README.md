# Web Assistant Agents

## 📖 项目简介

Web Assistant Agents

## 🚀 快速开始

### 环境要求

- Python 3.9+
- [uv](https://docs.astral.sh/uv/) (推荐的包管理器)

### 安装uv

```bash
# macOS/Linux
curl -LsSf https://astral.sh/uv/install.sh | sh

# Windows
powershell -c "irm https://astral.sh/uv/install.ps1 | iex"

# 或者使用pip
pip install uv
```

### 本地开发启动

```bash
# 克隆项目
git clone <repository-url>
cd web-assistant-agents

# 一键启动 (推荐)
./start.sh

# 自定义端口启动
./start.sh --port 8080


# 或者手动启动
uv sync                    # 安装依赖
uv run uvicorn app.main:app --reload --host localhost --port 8000
```

### 验证安装

启动成功后，访问以下地址验证服务：

- **API文档**: http://localhost:8000/docs
- **健康检查**: http://localhost:8000/api/v1/health

## 📚 API接口文档

### 核心端点

#### 1. 内容总结 `POST /api/v1/content/summarize`

智能总结文本内容或对话历史，支持流式和非流式响应，自动处理长内容和上下文预算。

**请求参数：**
```json
{
  "content": "要总结的长文本内容",
  "messages": [
    {"role": "user", "content": "用户消息"},
    {"role": "assistant", "content": "助手回复"}
  ],
  "stream": false,
  "max_tokens": 1000,
  "model": "ht::saas-deepseek-v3",
  "history_strategy": "summarize"
}
```

**参数说明：**
- `content` (可选): 要总结的文本内容，与 `messages` 至少提供一个
- `messages` (可选): 对话历史消息列表，格式为 `[{role, content}, ...]`
- `stream` (可选): 是否使用流式响应，默认 false
- `max_tokens` (可选): 生成结果的最大 token 数，默认 1000
- `model` (可选): 指定使用的模型，默认 "ht::saas-deepseek-v3"
- `history_strategy` (可选): 对话历史处理策略，可选值：
  - `"summarize"`: 智能压缩（保留尾部+头部摘要）
  - `"truncate"`: 直接截断（不调用 LLM 做摘要）

**功能特性：**
- **长内容处理**: 自动切块、并发压缩、合并摘要，确保最终调用可流式输出
- **上下文管理**: 智能预算控制，优先保留最新用户意图
- **多策略支持**: 支持内容总结和对话历史两种输入模式
- **模型兼容**: 支持多种模型，自动适配上下文窗口限制


**使用示例：**

**1. 内容总结（非流式）：**
```bash
curl -X POST "http://localhost:8000/api/v1/content/summarize" \
  -H "Content-Type: application/json" \
  -d '{
    "content": "这里是要总结的长文本内容...",
    "stream": false,
    "max_tokens": 500
  }'
```

**2. 对话历史智能压缩：**
```bash
curl -X POST "http://localhost:8000/api/v1/content/summarize" \
  -H "Content-Type: application/json" \
  -d '{
    "messages": [
      {"role": "user", "content": "请总结以下法律文档"},
      {"role": "assistant", "content": "好的，我来帮您总结..."}
    ],
    "history_strategy": "summarize",
    "stream": false,
    "max_tokens": 800
  }'
```

**3. 流式响应：**
```bash
curl -X POST "http://localhost:8000/api/v1/content/summarize" \
  -H "Content-Type: application/json" \
  -d '{
    "content": "长文本内容...",
    "stream": true,
    "model": "ht::saas-deepseek-v3"
  }'
```

**响应格式：**

**非流式响应：**
```json
{
  "choices": [
    {
      "message": {
        "content": "总结内容..."
      }
    }
  ]
}
```

**流式响应（SSE）：**
```
data: {"delta": "总结"}
data: {"delta": "内容"}
data: [DONE]
```

**错误响应：**
```json
{
  "error": "错误描述信息"
}
```

#### 2. 健康检查 `GET /api/v1/health`

检查服务健康状态。

**响应：**
```json
{
  "status": "healthy",
  "version": "1.0.0",
  "timestamp": "2024-01-01T00:00:00Z"
}
```

## 📁 项目结构

## 💡 使用示例

### 标准内容总结

```bash
curl -X POST "http://web-agents.sit.saas.htsc/api/v1/content/summarize" \
  -H "Content-Type: application/json" \
  -d '{
    "content": "人工智能（Artificial Intelligence，简称AI）是计算机科学的一个分支，它企图了解智能的实质，并生产出一种新的能以人类智能相似的方式做出反应的智能机器。",
    "stream": false,
    "language": "zh",
    "max_length": 100
  }'
```


```bash
curl -X POST "http://************:8000/api/v1/content/summarize" \
  -H "Content-Type: application/json" \
  -d '{
    "content": "人工智能（Artificial Intelligence，简称AI）是计算机科学的一个分支，它企图了解智能的实质，并生产出一种新的能以人类智能相似的方式做出反应的智能机器。",
    "stream": true
  }'
```

### 流式内容总结

```bash
curl -X POST "http://localhost:8000/api/v1/content/summarize" \
  -H "Content-Type: application/json" \
  -d '{
    "content": "这里是要总结的长文本内容...",
    "stream": true,
    "language": "zh"
  }'


curl -X POST 'http://*************/web/unauth/LLM_api_proxy/v1/chat/completions' \
  -H "Content-Type: application/json" \
  -d '{
    "model": "ht::saas-deepseek-v3",
    "messages": [
      {
        "role": "user",
        "content": [
                {
                    "type": "text",
                    "text": "描述这个图片"
                },
                {
                    "type": "image_url",
                    "image_url": {
                        "url": 'http://************:30908/test-2.png'
                        }
                }]
      }
    ],
    "stream": true,
    "max_tokens": 1000
}'
```
