#!/usr/bin/env python3
"""
测试并发流式翻译功能
"""
import asyncio
import json
from typing import AsyncGenerator


class MockLLMService:
    """模拟LLM服务 - 支持并发流式响应"""
    
    async def stream_chat_completion(self, messages, model=None, max_tokens=None, temperature=None):
        """模拟流式聊天补全"""
        # 根据输入文本生成不同的模拟响应
        text = messages[1]["content"] if len(messages) > 1 else "default"
        
        # 模拟不同文本的翻译速度和内容
        if "Hello" in text:
            mock_responses = [
                '{"choices": [{"delta": {"content": "你", "role": "assistant"}, "index": 0}]}',
                '{"choices": [{"delta": {"content": "好", "role": null}, "index": 0}]}',
                '{"choices": [{"delta": {"content": "，", "role": null}, "index": 0}]}',
                '{"choices": [{"delta": {"content": "你", "role": null}, "index": 0}]}',
                '{"choices": [{"delta": {"content": "好", "role": null}, "index": 0}]}',
                '{"choices": [{"delta": {"content": "吗", "role": null}, "index": 0}]}',
                '{"choices": [{"delta": {"content": "？", "role": null}, "index": 0}]}',
                '[DONE]'
            ]
            delay = 0.2
        elif "test" in text.lower():
            mock_responses = [
                '{"choices": [{"delta": {"content": "这", "role": "assistant"}, "index": 0}]}',
                '{"choices": [{"delta": {"content": "是", "role": null}, "index": 0}]}',
                '{"choices": [{"delta": {"content": "一", "role": null}, "index": 0}]}',
                '{"choices": [{"delta": {"content": "个", "role": null}, "index": 0}]}',
                '{"choices": [{"delta": {"content": "测试", "role": null}, "index": 0}]}',
                '[DONE]'
            ]
            delay = 0.15
        else:
            mock_responses = [
                '{"choices": [{"delta": {"content": "翻", "role": "assistant"}, "index": 0}]}',
                '{"choices": [{"delta": {"content": "译", "role": null}, "index": 0}]}',
                '{"choices": [{"delta": {"content": "结", "role": null}, "index": 0}]}',
                '{"choices": [{"delta": {"content": "果", "role": null}, "index": 0}]}',
                '[DONE]'
            ]
            delay = 0.1
        
        for response in mock_responses:
            yield response
            await asyncio.sleep(delay)  # 模拟不同的响应速度


class MockTranslationService:
    """模拟翻译服务 - 测试并发流式翻译"""
    
    def __init__(self):
        self.llm_service = MockLLMService()
    
    async def _stream_translate_single_text(self, text: str, index: int, src_lang: str, tgt_lang: str) -> AsyncGenerator[str, None]:
        """流式翻译单个文本 - 使用标准化增量格式"""
        try:
            print(f"[任务 {index}] 开始翻译: {text}")
            
            # 模拟构建翻译消息
            messages = [
                {"role": "system", "content": f"Translate from {src_lang} to {tgt_lang}"},
                {"role": "user", "content": text}
            ]
            
            # 调用模拟LLM流式服务
            stream_generator = self.llm_service.stream_chat_completion(messages=messages)
            
            # 处理流式响应
            accumulated_content = ""
            async for chunk in stream_generator:
                try:
                    chunk_data = chunk.strip()
                    
                    if not chunk_data or chunk_data == "[DONE]":
                        if chunk_data == "[DONE]":
                            break
                        continue
                    
                    # 解析JSON数据
                    chunk_json = json.loads(chunk_data)
                    
                    # 提取增量内容
                    if "choices" in chunk_json and chunk_json["choices"]:
                        choice = chunk_json["choices"][0]
                        if "delta" in choice and "content" in choice["delta"]:
                            delta_content = choice["delta"]["content"]
                            if delta_content:
                                accumulated_content += delta_content
                                
                                # 构建标准化增量响应格式
                                delta_response = {
                                    "delta": {
                                        "index": index,
                                        "text": text,
                                        "translateText": accumulated_content
                                    }
                                }
                                
                                yield f"data: {json.dumps(delta_response, ensure_ascii=False)}\n\n"
                                
                except (json.JSONDecodeError, KeyError) as e:
                    print(f"[任务 {index}] 解析失败: {str(e)}")
                    continue
            
            # 发送该文本的完成标记
            completion_response = {
                "delta": {
                    "index": index,
                    "text": text,
                    "translateText": accumulated_content,
                    "finished": True
                }
            }
            yield f"data: {json.dumps(completion_response, ensure_ascii=False)}\n\n"
            
            print(f"[任务 {index}] 翻译完成: '{text}' -> '{accumulated_content}'")
            
        except Exception as e:
            print(f"[任务 {index}] 翻译失败: {str(e)}")
            error_response = {
                "delta": {
                    "index": index,
                    "text": text,
                    "error": True,
                    "message": f"翻译异常: {str(e)}"
                }
            }
            yield f"data: {json.dumps(error_response, ensure_ascii=False)}\n\n"
    
    async def concurrent_stream_translate(self, texts: list) -> AsyncGenerator[str, None]:
        """并发流式翻译多个文本"""
        try:
            print(f"开始并发流式翻译 {len(texts)} 个文本")
            
            # 创建并发翻译任务
            tasks = []
            for idx, text in enumerate(texts):
                task = self._stream_translate_single_text(
                    text=text,
                    index=idx,
                    src_lang="en",
                    tgt_lang="zh"
                )
                tasks.append(task)
            
            # 使用异步生成器合并多个流式输出
            async def merge_streams():
                """合并多个流式输出"""
                output_queue = asyncio.Queue()
                active_tasks = set()
                
                async def collect_from_stream(stream_gen, task_id):
                    try:
                        async for chunk in stream_gen:
                            await output_queue.put((task_id, chunk))
                    except Exception as e:
                        print(f"收集流式数据失败 (任务 {task_id}): {str(e)}")
                    finally:
                        active_tasks.discard(task_id)
                        if not active_tasks:
                            await output_queue.put((None, None))  # 完成信号
                
                # 启动所有收集器
                for i, task in enumerate(tasks):
                    active_tasks.add(i)
                    asyncio.create_task(collect_from_stream(task, i))
                
                # 输出合并的流式数据
                while True:
                    try:
                        task_id, chunk = await asyncio.wait_for(output_queue.get(), timeout=10.0)
                        if task_id is None:  # 完成信号
                            break
                        yield chunk
                    except asyncio.TimeoutError:
                        print("流式翻译合并超时")
                        break
                    except Exception as e:
                        print(f"流式数据合并异常: {str(e)}")
                        break
            
            # 输出合并后的流式数据
            async for chunk in merge_streams():
                yield chunk
            
            # 发送最终完成响应
            yield "data: [DONE]\n\n"
            print("并发流式翻译完成")
            
        except Exception as e:
            print(f"并发流式翻译失败: {str(e)}")
            error_response = {
                "delta": {
                    "error": True,
                    "message": f"并发翻译异常: {str(e)}"
                }
            }
            yield f"data: {json.dumps(error_response, ensure_ascii=False)}\n\n"
            yield "data: [DONE]\n\n"


async def test_concurrent_stream_translation():
    """测试并发流式翻译功能"""
    print("=== 测试并发流式翻译功能 ===")
    
    service = MockTranslationService()
    
    # 测试文本
    test_texts = [
        "Hello, how are you?",
        "This is a test message.",
        "Good morning everyone!"
    ]
    
    print(f"测试文本: {test_texts}")
    print("\n--- 开始并发流式翻译 ---")
    
    # 记录翻译状态
    translation_states = {i: {"text": text, "translateText": "", "finished": False} 
                         for i, text in enumerate(test_texts)}
    
    chunk_count = 0
    async for chunk in service.concurrent_stream_translate(test_texts):
        chunk_count += 1
        print(f"\n[流式数据 {chunk_count}]")
        print(f"原始数据: {chunk.strip()}")
        
        # 解析并显示翻译进度
        if chunk.startswith("data: ") and not chunk.strip().endswith("[DONE]"):
            try:
                data_str = chunk[6:].strip()
                if data_str:
                    data = json.loads(data_str)
                    if "delta" in data:
                        delta = data["delta"]
                        if "index" in delta:
                            index = delta["index"]
                            if "translateText" in delta:
                                translation_states[index]["translateText"] = delta["translateText"]
                            if delta.get("finished"):
                                translation_states[index]["finished"] = True
                            
                            print(f"  -> 文本 {index}: '{delta.get('text', '')}' -> '{delta.get('translateText', '')}'")
                            if delta.get("finished"):
                                print(f"     [文本 {index} 翻译完成]")
            except Exception as e:
                print(f"  -> 解析失败: {e}")
        elif chunk.strip().endswith("[DONE]"):
            print("  -> 所有翻译完成")
    
    print(f"\n=== 最终翻译结果 ===")
    for i, state in translation_states.items():
        status = "✓" if state["finished"] else "✗"
        print(f"{status} 文本 {i}: '{state['text']}' -> '{state['translateText']}'")
    
    print(f"\n总共接收到 {chunk_count} 个流式数据块")
    print("=== 测试完成 ===")


if __name__ == "__main__":
    asyncio.run(test_concurrent_stream_translation())
