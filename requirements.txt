# 精简的生产环境依赖 - 只包含必要的包
# Web框架
fastapi==0.116.1
uvicorn[standard]==0.35.0
gunicorn==23.0.0

# LangGraph框架
langgraph==0.5.4
langchain-core==0.3.71

# HTTP客户端
aiohttp==3.12.14
requests==2.32.4

# 数据处理
pydantic==2.11.7
pydantic-settings==2.10.1
python-multipart==0.0.20

# 配置和日志
python-dotenv==1.1.1
structlog==25.4.0
rich==14.0.0

# 系统监控
psutil==7.0.0

# 图像处理
Pillow==11.3.0

# 文档处理
beautifulsoup4==4.13.4
lxml==6.0.0

# Tokenization
tiktoken==0.8.0
