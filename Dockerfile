# 使用Python 3.9官方镜像
FROM repo-dev.htsc/public-cncp-image-base-local/platform/001407/node_python:22_313


# 设置工作目录
WORKDIR /app


RUN ln -sf /usr/local/python3/bin/python3 /usr/bin/python


# 安装 uv uvx
RUN pip install uv -i http://repo.htzq.htsc.com.cn/repository/htscpypi/simple/ --trusted-host repo.htzq.htsc.com.cn

RUN uv --version

# 复制项目配置文件和README
COPY pyproject.toml uv.lock README.md ./

# 使用uv安装依赖（只安装生产依赖，指定镜像源，不使用frozen模式）
RUN uv sync --no-dev --index-url http://repo.htzq.htsc.com.cn/repository/htscpypi/simple/ --trusted-host repo.htzq.htsc.com.cn

# 复制应用代码
COPY app/ ./app/

# 复制环境变量文件
COPY .env ./

# 复制启动脚本并设置执行权限
COPY start.sh ./
RUN chmod +x start.sh

# 暴露端口
EXPOSE 8000

# 激活虚拟环境并启动应用
CMD ["bash", "-c", "source .venv/bin/activate && python -m uvicorn app.main:app --host 0.0.0.0 --port 8000"]