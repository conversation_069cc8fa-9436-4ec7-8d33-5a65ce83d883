"""
翻译Agent集成测试
"""
import pytest
import asyncio
from app.agents.translation.translation_agent import TranslationAgent
from app.core.models import AgentType


@pytest.mark.asyncio
async def test_translation_agent_doubao():
    """测试Doubao翻译"""
    agent = TranslationAgent(AgentType.TRANSLATION)
    
    # 测试数据
    question = [
        {"text": "Hello"},
        {"text": "World"}
    ]
    
    translate_options = {
        "src_lang": "en",
        "tgt_lang": "zh"
    }
    
    # 执行翻译
    result = await agent.process_translation(
        question=question,
        stream=False,
        translate_options=translate_options,
        provider="doubao"
    )
    
    # 验证结果
    assert result is not None
    assert "code" in result
    assert "data" in result
    assert isinstance(result["data"], list)
    assert len(result["data"]) == 2


@pytest.mark.asyncio
async def test_translation_agent_llm():
    """测试LLM翻译"""
    agent = TranslationAgent(AgentType.TRANSLATION)
    
    # 测试数据
    question = [
        {"text": "Hello"},
        {"text": "World"}
    ]
    
    translate_options = {
        "src_lang": "en",
        "tgt_lang": "zh"
    }
    
    # 执行翻译
    result = await agent.process_translation(
        question=question,
        stream=False,
        translate_options=translate_options,
        provider="llm_translate"
    )
    
    # 验证结果
    assert result is not None
    assert "code" in result
    assert "data" in result
    assert isinstance(result["data"], list)
    assert len(result["data"]) == 2


@pytest.mark.asyncio
async def test_translation_agent_stream():
    """测试流式翻译"""
    agent = TranslationAgent(AgentType.TRANSLATION)
    
    # 测试数据
    question = [
        {"text": "Hello"}
    ]
    
    translate_options = {
        "src_lang": "en",
        "tgt_lang": "zh"
    }
    
    # 执行流式翻译
    result = await agent.process_translation(
        question=question,
        stream=True,
        translate_options=translate_options,
        provider="llm_translate"
    )
    
    # 验证流式结果
    chunks = []
    async for chunk in result:
        chunks.append(chunk)
    
    assert len(chunks) > 0
    assert any("data:" in chunk for chunk in chunks)


if __name__ == "__main__":
    # 运行测试
    asyncio.run(test_translation_agent_llm())
