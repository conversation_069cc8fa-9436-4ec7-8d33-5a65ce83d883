#!/usr/bin/env python3
"""
测试流式翻译功能
"""
import asyncio
import json
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app.agents.translation.translation_agent import TranslationAgent
from app.core.models import AgentType


async def test_stream_translation():
    """测试流式翻译功能"""
    print("=== 测试流式翻译功能 ===")
    
    # 创建翻译代理
    agent = TranslationAgent(AgentType.TRANSLATION)
    
    # 测试数据
    test_questions = [
        {"text": "Hello, how are you today?"},
        {"text": "This is a test message for translation."}
    ]
    
    print(f"测试文本: {test_questions}")
    
    # 测试1: Doubao提供商 + 流式模式（应该返回非流式结果的流式包装）
    print("\n--- 测试1: Doubao + 流式模式 ---")
    try:
        result = await agent.process_translation(
            question=test_questions,
            stream=True,
            translate_options={"src_lang": "en", "tgt_lang": "zh"},
            provider="doubao"
        )
        
        if hasattr(result, '__aiter__'):
            print("返回了流式生成器")
            async for chunk in result:
                print(f"流式数据: {chunk}")
        else:
            print(f"返回了非流式结果: {result}")
            
    except Exception as e:
        print(f"测试1失败: {str(e)}")
    
    # 测试2: LLM提供商 + 非流式模式
    print("\n--- 测试2: LLM + 非流式模式 ---")
    try:
        result = await agent.process_translation(
            question=test_questions,
            stream=False,
            translate_options={"src_lang": "en", "tgt_lang": "zh"},
            provider="llm_translate"
        )
        
        print(f"非流式结果: {result}")
        
    except Exception as e:
        print(f"测试2失败: {str(e)}")
    
    # 测试3: LLM提供商 + 流式模式（真正的流式翻译）
    print("\n--- 测试3: LLM + 流式模式 ---")
    try:
        result = await agent.process_translation(
            question=test_questions,
            stream=True,
            translate_options={"src_lang": "en", "tgt_lang": "zh"},
            provider="llm_translate"
        )
        
        if hasattr(result, '__aiter__'):
            print("返回了流式生成器")
            chunk_count = 0
            async for chunk in result:
                chunk_count += 1
                print(f"流式数据 {chunk_count}: {chunk[:100]}...")  # 只显示前100个字符
                if chunk_count >= 5:  # 限制显示数量
                    print("... (更多流式数据)")
                    break
        else:
            print(f"返回了非流式结果: {result}")
            
    except Exception as e:
        print(f"测试3失败: {str(e)}")
    
    print("\n=== 测试完成 ===")


if __name__ == "__main__":
    asyncio.run(test_stream_translation())
