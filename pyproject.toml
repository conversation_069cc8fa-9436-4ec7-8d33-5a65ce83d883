[project]
name = "web-assistant-agents"
version = "1.0.0"
description = "基于LangGraph的多智能体浏览器AI助手服务框架"
authors = [
    {name = "Web Assistant Team", email = "<EMAIL>"}
]
readme = "README.md"
license = {text = "MIT"}
requires-python = ">=3.9"
keywords = ["ai", "agents", "langgraph", "fastapi", "llm"]
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Topic :: Software Development :: Libraries :: Python Modules",
    "Topic :: Scientific/Engineering :: Artificial Intelligence",
]

dependencies = [
    # Web框架
    "fastapi>=0.100.0",
    "uvicorn[standard]>=0.20.0",
    "gunicorn>=21.0.0",
    
    # LangGraph框架
    "langgraph>=0.1.0",
    "langchain-core>=0.1.0",
    
    # HTTP客户端
    "aiohttp>=3.8.0",
    "requests>=2.28.0",
    
    # 数据处理
    "pydantic>=2.0.0",
    "pydantic-settings>=2.0.0",
    "python-multipart>=0.0.6",
    

    
    # 工具库
    "python-dotenv>=1.0.0",
    "structlog>=23.0.0",
    "rich>=13.0.0",
    
    # 系统监控
    "psutil>=5.9.0",
    
    # 图像处理
    "Pillow>=10.0.0",
    
    # 文档处理
    "beautifulsoup4>=4.12.0",
    "lxml>=4.9.0",
]

[project.optional-dependencies]
dev = [
    # 测试
    "pytest>=7.4.0",
    "pytest-asyncio>=0.21.0",
    "pytest-cov>=4.1.0",
    "httpx>=0.25.0",
    
    # 开发工具
    "black>=23.0.0",
    "isort>=5.12.0",
    "flake8>=6.0.0",
    "mypy>=1.0.0",
    
    # 类型检查
    "types-requests",
    "types-aiofiles",
]



[project.urls]
Homepage = "https://github.com/your-org/web-assistant-agents"
Documentation = "https://web-assistant-agents.readthedocs.io/"
Repository = "https://github.com/your-org/web-assistant-agents.git"
Issues = "https://github.com/your-org/web-assistant-agents/issues"

[project.scripts]
web-assistant = "app.main:main"

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.hatch.build.targets.wheel]
packages = ["app"]

[tool.black]
line-length = 100
target-version = ['py39']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.isort]
profile = "black"
line_length = 100
multi_line_output = 3
include_trailing_comma = true
force_grid_wrap = 0
use_parentheses = true
ensure_newline_before_comments = true

[tool.pytest.ini_options]
minversion = "6.0"
addopts = "-ra -q --strict-markers --strict-config"
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
asyncio_mode = "auto"

[tool.mypy]
python_version = "3.9"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[[tool.mypy.overrides]]
module = [
    "langgraph.*",
    "langchain_core.*",
    "structlog.*",
    "prometheus_client.*",
]
ignore_missing_imports = true

[tool.coverage.run]
source = ["app"]
omit = [
    "*/tests/*",
    "*/test_*",
    "*/__pycache__/*",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]
